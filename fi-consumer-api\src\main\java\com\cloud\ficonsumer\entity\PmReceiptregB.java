package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 收票登记子表
 */
@Data
@ApiModel("收票登记子表")
public class PmReceiptregB {

    @ApiModelProperty("收票登记子表主键")
    private String pkReceiptregB;

    @ApiModelProperty("合同编号")
    private String billCode;

    @ApiModelProperty("行号")
    private Integer rowno;

    @ApiModelProperty("项目")
    private String pkProject;

    @ApiModelProperty("发票金额")
    private BigDecimal currMny;

    @ApiModelProperty("税率")
    private BigDecimal taxrate;

    @ApiModelProperty("税额")
    private BigDecimal tax;

    @ApiModelProperty("发票价税合计")
    private BigDecimal totTaxmny;
}
