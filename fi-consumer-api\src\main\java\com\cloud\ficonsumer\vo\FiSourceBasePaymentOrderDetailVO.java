package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/4.
 */
@Data
public class FiSourceBasePaymentOrderDetailVO {

    /**
     * 单据类型 当F3-Cxx-01项目付款的时候 （F3-Cxx-02采购付款时候无）
     * 4D42 付款合同
     * 4D83 费用结算单
     */
    @ApiModelProperty("付款类型编码")
    private String pkBilltype;

    @ApiModelProperty("来源主键")
    private String srcBillid;

    @ApiModelProperty("行号")
    private String rowno;

    @ApiModelProperty("项目")
    private String project;

    @ApiModelProperty("组织本币无税金额")
    private String localNotaxDe;

    @ApiModelProperty("税率")
    private String taxrate;

    @ApiModelProperty("行号")
    private String localTaxDe;

    @ApiModelProperty("行号")
    private String localMoneyDe;

}
