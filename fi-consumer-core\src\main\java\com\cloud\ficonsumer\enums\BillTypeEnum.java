package com.cloud.ficonsumer.enums;

/**
 * 单据枚举
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
public enum BillTypeEnum {

    ORDER("fi_source_order", "订单源数据主表"),
    ORDER_DETAIL("fi_source_order_detail", "订单源数据详情表"),
    BASE_ORDER("fi_source_base_order", "订单源数据主表"),
    BASE_ORDER_DETAIL("fi_source_base_order_detail", "订单源数据详情表"),
    COST_ORDER("fi_source_cost_order", "费用结算单订单源数据主表"),
    COST_ORDER_DETAIL("fi_source_cost_order_detail", "费用结算单订单源数据详情表"),
    VIRTUAL_ORDER("fi_source_virtual_order", "虚拟采购订单源数据主表"),
    VIRTUAL_ORDER_DETAIL("fi_source_virtual_order_detail", "虚拟采购订单源数据详情表"),
    REIMBURSEMENT("fi_source_reimbursement", "通用付款单主表"),
    REIMBURSEMENT_DETAIL("fi_source_reimbursement_detail", "通用付款单情表"),
    PAYMENT("fi_source_payment", "通用报销单主表"),
    PAYMENT_DETAIL("fi_source_payment_detail", "通用报销单情表"),
    RECEIVABLE("fi_source_receivable", "应收单主表"),
    RECEIVABLE_DETAIL("fi_source_receivable_detail", "应收单情表"),
    STORE("fi_source_store", "采购入库主表"),
    STORE_DETAIL("fi_source_store_detail", "采购入库情表"),
    NEW_STORE("fi_source_new_store", "新采购入库主表"),
    NEW_STORE_DETAIL("fi_source_new_store_detail", "新采购入库情表"),
    ACCOUNTING("fi_source_accounting", "会计凭证源数据主表"),
    ACCOUNTING_DETAIL("fi_source_accounting_detail", "会计凭证源数据详情表"),
    ACCOUNTING_DOCFREE("fi_source_accounting_docfree", "凭证记账辅助核算源数据明细表"),
    PU_PAYABLE("fi_source_pu_payable", "采购应付源数据主表"),
    PU_PAYABLE_DETAIL("fi_source_pu_payable_detail", "采购应付源数据详情表"),
    PU_PAYBILL("fi_source_pu_paybill", "采购付款源数据主表"),
    PU_PAYBILL_DETAIL("fi_source_pu_paybill_detail", "采购应付源数据详情表"),
    PROJ_ESTI_PAYABLE("fi_source_projesti_paybill", "项目暂估应付源数据主表"),
    PROJ_ESTI_PAYABLE_DETAIL("fi_source_projesti_detail", "项目暂估应付源数据详情表"),
    PROJ_PAYBILL("fi_source_projpaybill", "项目付款源数据主表"),
    PROJ_PAYBILL_DETAIL("fi_source_projpaybill_detail", "项目付款源数据详情表"),
    PROJ_SUB_PAYABLE("fi_source_projsub_payable", "项目分包应付源数据主表"),
    PROJ_SUB_PAYABLE_DETAIL("fi_source_projsub_payable_detail", "项目分包应付源数据详情表"),
    SALE_ORDER("fi_source_sale_order", "销售订单源数据主表"),
    SALE_ORDER_DETAIL("fi_sourc_sale_order_detail", "销售订单源数据详情表"),
    FILE_OCR_INFO("fi_source_file_ocr_info", "进项发票源数据详情"),
    FILE_INFO("fi_source_file_info", "文件信息源");

    private String code;
    private String name;

    BillTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取枚举类
     *
     * @param code
     * @return
     */
    public static BillTypeEnum getByCode(Integer code) {
        for (BillTypeEnum en : values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return null;
    }

}
