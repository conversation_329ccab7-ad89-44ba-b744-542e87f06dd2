

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiConvertBaseOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertBaseOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceBaseOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceBaseOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceBaseOrder;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceBaseOrderQueryVO;

import java.util.List;

/**
 * 订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:09
 */
public interface FiSourceBaseOrderService extends IService<FiSourceBaseOrder> {

    boolean pushAgain(String s);

    /**
     * 订单转换前分页查询
     *
     * @return
     */
    Page<FiSourceBaseOrderDTO> beforeConvertDataPage(Page page, FiSourceBaseOrderQueryVO queryVO);

    /**
     * 转换前详情查询
     *
     * @param page
     * @param queryVO
     * @return
     */
    Page<FiSourceBaseOrderDetailDTO> getBeforeConvertDataPage(Page page, FiSourceBaseOrderQueryVO queryVO);

    /**
     * 主体表转换后单条字段
     *
     * @param pk
     * @return
     */
    List<FiConvertBaseOrderDTO> getConvertList(String pk);

    /**
     * 获取转换后详情分页数据
     *
     * @param page
     * @param pk
     * @return
     */
    Page<FiConvertBaseOrderDetailDTO> getConvertData(Page page, String pk);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}
