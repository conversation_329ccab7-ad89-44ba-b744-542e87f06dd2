

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:09
 */
@Data
@TableName("fi_source_base_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "订单源数据主表")
public class FiSourceBaseOrder extends BaseEntity<FiSourceBaseOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * MDM基础组织编码
     */
    @ApiModelProperty(value="MDM基础组织编码")
    private String prvMkCode;

    /**
     * 采购订单号
     */
    @ApiModelProperty(value="采购订单号")
    private String purOrdNo;

    /**
     * 订单编号
     */
    @ApiModelProperty(value="所属单位")
    private String aflUnit;

    /**
     * 采购组织
     */
    @ApiModelProperty(value="创建时间")
    private String creTime;

    /**
     * 凭证类型
     */
    @ApiModelProperty(value="凭证类型")
    private String purOrdTyp;

    /**
     * 合同编号
     */
    @ApiModelProperty(value="合同编号")
    private String contractNo;

    /**
     * 公司代码
     */
    @ApiModelProperty(value="公司代码")
    private String firmCode;

    /**
     * 创建对象的人员名称
     */
    @ApiModelProperty(value="创建对象的人员名称")
    private String crePersNm;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value="供应商编码")
    private String supCod;

    /**
     * 订单金额(不含税总额)
     */
    @ApiModelProperty(value="订单金额(不含税总额)")
    private String totAmExTax;

    /**
     * 支付比例
     */
    @ApiModelProperty(value="支付比例")
    private String pyRt;

    /**
     * 货币
     */
    @ApiModelProperty(value="货币")
    private String currCod;

    /**
     * 汇率
     */
    @ApiModelProperty(value="汇率")
    private String exchRat;

    /**
     * 订单税额
     */
    @ApiModelProperty(value="订单税额")
    private String purOrdTaxAmount;

    /**
     * 采购订单类型描述
     */
    @ApiModelProperty(value="采购订单类型描述")
    private String purOrdTypDesc;

    /**
     * 传输日期
     */
    @ApiModelProperty(value="传输日期")
    private String transmissionDate;

    /**
     * 出票方
     */
    @ApiModelProperty(value="出票方")
    private String drawerCode;

    /**
     * 利润中心
     */
    @ApiModelProperty(value="利润中心")
    private String proCen;

    /**
     * 请购单号
     */
    @ApiModelProperty(value="请购单号")
    private String purReq;

    /**
     * 项目类别
     */
    @ApiModelProperty(value="项目类别")
    private String prjType;

    /**
     * 采购组织
     */
    @ApiModelProperty(value="采购组织")
    private String purOrg;

    /**
     * 采购组
     */
    @ApiModelProperty(value="采购组")
    private String purGro;

    /**
     * 商城订单号
     */
    @ApiModelProperty(value="商城订单号")
    private String mallProcureOrder;

    /**
     * 数据来源系统
     */
    @ApiModelProperty(value="数据来源系统")
    private String dataSouSys;

    /**
     * 组织
     */
    @ApiModelProperty(value="组织")
    private String pkOrg;

    /**
     * 组织名称
     */
    @ApiModelProperty(value="组织名称")
    private String pkOrgName;

    /**
     * 推送时间
     */
    @ApiModelProperty(value="推送时间")
    private String pushTime;

    /**
     * 1 推送中 2推送成功 3推送失败
     */
    @ApiModelProperty(value="1 推送中 2推送成功 3推送失败")
    private Integer pushStatus;

    /**
     * 推送成功失败信息
     */
    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    /**
     * 重试次数
     */
    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    /**
     * 0 未就绪 1 就绪
     */
    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;

    /**
     * 主键值
     * 应付单主键和预付款主键
     * 当单据是预付款的时候pkMain是预付款主键，pkPayMain为空（当收票登记核销上游能关联预付款的时候更新pkPayMain值）
     * 当单据是收票登记的时候pkMain是应付单主键，pkPayMain是应付单主键
     */
    @ApiModelProperty(value="主键值")
    private String pkMain;

    /**
     * 应付单主键
     */
    @ApiModelProperty(value="应付单主键")
    private String pkPayMain;

    /**
     * 来源类型（付款合同、费用结算）
     */
    @ApiModelProperty(value="来源类型")
    private String source;

    /**
     * 来源单据号（付款合同、费用结算）
     */
    @ApiModelProperty(value="来源单据号")
    private String sourceNo;

    /**
     * 序列号
     */
    @ApiModelProperty(value="序列号")
    private Integer maxSource;

}
