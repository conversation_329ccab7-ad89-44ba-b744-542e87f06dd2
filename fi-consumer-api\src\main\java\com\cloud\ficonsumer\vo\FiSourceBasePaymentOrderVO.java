package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/4.
 */
@Data
public class FiSourceBasePaymentOrderVO {

    /**
     * 拉取标识
     */
    @ApiModelProperty(value="单据详情主键")
    private String def83;

    /**
     * 单据类型 当F3-Cxx-01项目付款的时候 （F3-Cxx-02采购付款时候无）
     * 4D42 付款合同
     * 4D83 费用结算单
     */
    @ApiModelProperty("付款类型编码")
    private String pkBilltype;

    @ApiModelProperty("付款主键")
    private String pkPaybill;

    @ApiModelProperty("财务组织")
    private String pkOrg;

    @ApiModelProperty("预付款单据号")
    private String billno;

    @ApiModelProperty("付款合同或费用结算单据号")
    private String billCode;

    @ApiModelProperty("生效日期")
    private String effectdate;

    @ApiModelProperty("制单人")
    private String billmaker;

    @ApiModelProperty("供应商")
    private String supplier;

    @ApiModelProperty("组织本币无税金额")
    private String localNotaxDe;

    @ApiModelProperty("税额")
    private String localTaxDe;

}
