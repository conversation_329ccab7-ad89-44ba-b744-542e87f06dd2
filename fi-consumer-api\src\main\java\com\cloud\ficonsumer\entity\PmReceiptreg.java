package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 收票登记主表
 */
@Data
@ApiModel("收票登记主表")
public class PmReceiptreg {

    @ApiModelProperty("收票登记主键")
    private String pkReceiptreg;

    @ApiModelProperty("单据号")
    private String billCode;

    @ApiModelProperty("财务组织")
    private String pkOrgV;

    @ApiModelProperty("审批日期")
    private Date audittime;

    @ApiModelProperty("供应商")
    private String pkSupplierV;

    @ApiModelProperty("发票金额")
    private BigDecimal currMny;

    @ApiModelProperty("发票税额")
    private BigDecimal tax;
}
