# 项目应付付款业务流程优化代码实现Prompt

## 任务概述

基于已完成的需求分析和技术方案，需要对现有代码进行修改，实现项目应付付款业务流程的三种处理模式，解决税率信息来源不一致的问题。

## 业务背景

项目应付单、项目付款单源头订单来源于付款合同（pm_contr）和费用结算（pm_feebalance）。由于付款合同中维护了税率信息，在项目应付单传发票信息到业财库时，业财库计算税额、含税金额等相关字段时使用的税率会从合同上获取，而不是取发票的税率，导致了计算错误。

## 三种业务流程

### 流程1：无预付款直接开票付款
- 供应商直接开票，在收票登记节点维护票面信息
- 虚拟订单取合同单号序列递增
- 以票面信息作为订单传递到业财库
- **订单同步成功后，传递应付单信息给财务中台**

### 流程2：预付款流程
- 从预付款单虚拟出一张订单
- 订单单号取合同单号并序列递增
- 传递虚拟订单到业财库
- **订单同步成功后，传递付款单信息给财务中台**

### 流程3：正式付款冲抵预付款
- 供应商开具发票，发票冲抵预付款金额
- 检查预付款是否已经核销，如果未核销抛出异常
- 如果已核销，从虚拟订单表中根据预付款单号找到业财库订单单号
- 使用相同的订单单号，用当前票的金额覆盖之前预付款传过去的订单
- **订单更新成功后，传递应付单信息给财务中台**

## 核心技术要求

### 1. 核销检查逻辑
- 检查付款合同是否存在预付款
- 检查预付款是否已核销
- 获取预付款对应的虚拟订单单号

### 2. 虚拟订单单号生成
- 集成到`FiSourceBaseOrderService`中
- 生成基于合同单号的序列递增订单号
- 支持订单单号复用逻辑

### 3. SQL查询优化
- 在`DmMapper.xml`中实现SQL，而不是接口注解
- SQL要简洁易懂，部分逻辑在代码中实现
- 核销检查服务整体逻辑要清晰易懂

## 需要修改的文件

### 1. 核心服务类
- `fi-consumer-core/src/main/java/com/cloud/ficonsumer/service/impl/FiSourcePuPayableServiceImpl.java`
- `fi-consumer-core/src/main/java/com/cloud/ficonsumer/service/FiSourceBaseOrderService.java`
- `fi-consumer-core/src/main/java/com/cloud/ficonsumer/service/impl/FiSourceBaseOrderServiceImpl.java`

### 2. Context类
- `fi-consumer-core/src/main/java/com/cloud/ficonsumer/builder/context/YgInvoiceCheckContext.java`

### 3. 数据访问层
- `fi-consumer-core/src/main/java/com/cloud/ficonsumer/mapper/DmMapper.java`
- `fi-consumer-core/src/main/resources/mapper/DmMapper.xml`

### 4. 新增服务
- 创建`PrepaymentVerificationService`接口和实现类
- 创建相关的VO和枚举类

### 5. 枚举类
- 修改`PushStatusEnum`，新增核销检查失败等状态

## 关键SQL查询

### 预付款查询（简化版）
```sql
SELECT COUNT(1) 
FROM ap_payitem 
WHERE prepay = 1 
  AND src_billid = #{pkContr}
  AND dr = 0
```

### 预付款核销查询（简化版）
```sql
SELECT COUNT(1) 
FROM arap_verifydetail v
INNER JOIN ap_payablebill pb ON v.pk_bill = pb.pk_payablebill
INNER JOIN ap_payableitem pi ON pb.pk_payablebill = pi.pk_payablebill
WHERE pi.src_billid = #{pkContr}
  AND pb.pk_tradetype = 'F1-Cxx-01'
  AND v.dr = 0 
  AND pb.dr = 0 
  AND pi.dr = 0
```

### 获取虚拟订单单号
```sql
SELECT bill_code 
FROM fi_source_virtual_order 
WHERE pk_contr = #{pkContr} 
  AND del_flag = 0 
ORDER BY create_time DESC 
LIMIT 1
```

## 实现要求

### 1. 代码质量
- 保持与现有代码架构的兼容性
- 使用已有的Builder模式和分阶段构建机制
- 最小化对现有代码的影响
- 添加详细的注释和日志

### 2. 异常处理
- 统一异常处理机制
- 新增核销检查失败、订单单号生成失败等异常类型
- 完善错误信息和日志记录

### 3. 性能考虑
- SQL查询要高效，避免复杂的多表关联
- 合理使用缓存机制
- 避免重复查询

### 4. 测试友好
- 代码要便于单元测试
- 业务逻辑要清晰分离
- 依赖注入要合理

## 参考文档

请参考以下两个文档了解详细的需求和技术方案：
- `项目应付付款业务流程优化需求文档.md`
- `项目应付付款业务流程优化技术实现方案.md`

## 实施步骤建议

### 第一步：创建基础服务
1. 创建`PrepaymentVerificationService`接口和实现类
2. 在`DmMapper`中添加核销查询方法
3. 在`FiSourceBaseOrderService`中添加虚拟订单单号生成方法

### 第二步：修改Context类
1. 在`YgInvoiceCheckContext`中添加新字段和方法
2. 新增Builder方法支持核销检查和订单号生成
3. 实现业务流程类型检测逻辑

### 第三步：修改主流程
1. 修改`FiSourcePuPayableServiceImpl`的push方法
2. 集成核销检查和订单号生成逻辑
3. 增强异常处理机制

### 第四步：测试验证
1. 编写单元测试验证核心逻辑
2. 进行集成测试验证整体流程
3. 业务场景测试验证三种流程

## 注意事项

1. **保持向后兼容**：修改不能影响现有的采购应付流程
2. **数据一致性**：确保并发情况下的数据一致性
3. **性能影响**：新增的查询不能显著影响现有性能
4. **错误处理**：要有完善的错误处理和回滚机制
5. **日志记录**：关键步骤要有详细的日志记录

请基于以上要求，对相关代码文件进行修改实现。如果有任何疑问或需要澄清的地方，请及时提出。
