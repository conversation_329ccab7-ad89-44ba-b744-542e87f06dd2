package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.builder.YgInvoiceCheckVOBuilder;
import com.cloud.ficonsumer.builder.context.YgInvoiceCheckContext;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiSourcePuPayableDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.dto.YgResp;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.enums.*;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourcePuPayableMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

import static com.cloud.ficonsumer.vo.PayablePreResult.PayablePreEnum.*;

/**
 * 采购应付源数据主表服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourcePuPayableServiceImpl extends ServiceImpl<FiSourcePuPayableMapper, FiSourcePuPayable> implements FiSourcePuPayableService {

    private final FiSourcePuPayableDetailService detailService;
    private final FiConvertPuPayableService convertService;
    private final FiConvertPuPayableDetailService convertDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final YgConfig ygConfig;
    private final YgInvoiceCheckVOBuilder ygInvoiceCheckVOBuilder;
    private final PlatformLogService platformLogService;
    private final DmMapper dmMapper;
    private final FiSourceBaseOrderService fiSourceBaseOrderService;
    private final FiSourceBaseOrderDetailService fiSourceBaseOrderDetailService;

    @Override
    public boolean push(String pkPayablebill) {
        // 获取源数据
        FiSourcePuPayable source = this.getOne(Wrappers.<FiSourcePuPayable>lambdaQuery()
                .eq(FiSourcePuPayable::getPkPayablebill, pkPayablebill));
        List<FiSourcePuPayableDetail> sourceDetails = detailService.list(Wrappers.<FiSourcePuPayableDetail>lambdaQuery()
                .eq(FiSourcePuPayableDetail::getPkPayablebill, pkPayablebill));

        try {
            // 第一阶段：订单同步（仅项目应付需要）
            // 初步构建Context，用于加工fi_source_base_order
            YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                    .withSourceData(source, sourceDetails)
                    .withTopBill()          // 上游单据信息（收票登记）
                    .withSourceBill()       // 源头单据信息（付款合同或费用结算单）
                    .build();

            // 若为项目应付，需要首先同步订单
            if (context.isProjectPayable()) {
                context = context.continueWith()
                        .withPayablePreResult()        // 预付款核销状态检查
                        .build();

                PayablePreResult payablePreResult = context.getPayablePreResult();
                PayablePreResult.PayablePreEnum payablePreEnum = payablePreResult.getPayablePreEnum();

                // 预付款未核销，无法同步订单
                if (PRE_UNVERIFIED.equals(payablePreEnum)) {
                    throw new CheckedException("存在预付款，预付款未核销");
                }

                turnOrderByPayable(source, sourceDetails);
            }

            // 第二阶段：应付单转换数据
            log.info("开始转换应付单数据，单据号: {}", source.getBillno());
            convertData(source, sourceDetails);

            // 获取转换后的数据并更新到context中
            FiConvertPuPayable convert = convertService.getOne(Wrappers.<FiConvertPuPayable>lambdaQuery()
                    .eq(FiConvertPuPayable::getPkPayablebill, source.getPkPayablebill()));
            List<FiConvertPuPayableDetail> convertDetails = convertDetailService.list(Wrappers.<FiConvertPuPayableDetail>lambdaQuery()
                    .eq(FiConvertPuPayableDetail::getPkPayablebill, source.getPkPayablebill()));

            // 将转换数据添加到context中，继续构建
            context = context.continueWith()
                    .withConvertData(convert, convertDetails)
                    .build();

            log.info("应付单数据转换完成，单据号: {}", source.getBillno());

            // 第三阶段：应付单推送
            log.info("开始推送应付单，单据号: {}", source.getBillno());

            // 继续构建完整的context用于推送
            context = context.continueWith()
                    .withBasicInfo()        // 基础信息（组织、供应商、项目等）
                    .withCosTypeInfo()      // 费用类型信息
                    .withInvoiceInfo()      // 发票信息
                    .withAccountInfo()      // 账户信息
                    .withImageInfo()        // 影像信息
                    .withValidation()       // 业务校验
                    .build();

            pushToYgPlatform(source, context);
            log.info("应付单推送完成，单据号: {}", source.getBillno());

        } catch (Exception e) {
            // 统一异常处理
            handleUnifiedException(source, e);
            throw new CheckedException("【采购-发票校验申请服务】处理失败:" + e.getMessage());
        }

        return true;
    }

    private void turnOrderByPayable(YgInvoiceCheckContext context) {
        // 收票登记
        PmReceiptreg pmReceiptreg = context.getPmReceiptreg();
        List<PmReceiptregB> pmReceiptregBS = context.getPmReceiptregBS();

        // 预付款核销检查
        PayablePreResult payablePreResult = context.getPayablePreResult();
        PayablePreResult.PayablePreEnum payablePreEnum = payablePreResult.getPayablePreEnum();

        FiSourceBaseOrder baseOrder = new FiSourceBaseOrder();
        baseOrder.setPkMain(context.getSource().getPkPayablebill());
        baseOrder.setPkPayMain(payablePreResult.getPrePkPaybill());
        baseOrder.setPkOrg(context.getSource().getPkOrg());
        baseOrder.setPkOrgName(context.getSource().getPkOrgName());
        String prvMkCode = "00003010";
        baseOrder.setPrvMkCode(prvMkCode);
        baseOrder.setAflUnit(context.getSource().getPkOrg());
        baseOrder.setCreTime("");
        baseOrder.setCrePersNm(context.getSource().getBillmaker());
        baseOrder.setSupCod(pmReceiptreg.getPkSupplier());
        baseOrder.setPurOrdTyp("0L08");
        baseOrder.setDataSouSys("ZJYJ");

        // 当前应付单核销预付款
        if (VERIFIED.equals(payablePreResult.getPayablePreEnum())) {
            // 删除预付款单虚拟的订单
            FiSourceBaseOrder prePaybillOrder = fiSourceBaseOrderService.getOne(Wrappers.<FiSourceBaseOrder>lambdaQuery()
                    .eq(FiSourceBaseOrder::getPkMain, payablePreResult.getPrePkPaybill()));
            fiSourceBaseOrderService.remove(Wrappers.<FiSourceBaseOrder>lambdaQuery()
                            .eq(FiSourceBaseOrder::getPkMain, payablePreResult.getPrePkPaybill()));
            // 延用预付款单虚拟的订单号
            baseOrder.setSource(prePaybillOrder.getSource());
            baseOrder.setSourceNo(prePaybillOrder.getSourceNo());
            baseOrder.setMaxSource(prePaybillOrder.getMaxSource());
        }
        // 当前应付单未核销预付款
        else if (Arrays.asList(NO_PRE, NON_CURRENT_VERIFIED).contains(payablePreEnum)) {
            // 源头单据号
            if (StringUtils.isNotEmpty(payablePreResult.getPkContr())) {
                baseOrder.setSource("4D42");
                baseOrder.setSourceNo(payablePreResult.getContractNo());
            } else if (StringUtils.isNotEmpty(payablePreResult.getPkFeebalance())) {
                baseOrder.setSource("4D83");
                baseOrder.setSourceNo(payablePreResult.getFeebalanceNo());
            } else {
                throw new CheckedException("未找到单据编号");
            }
            // 相同源头单据号的递增序列号
            FiSourceBaseOrder maxSourceOrder = fiSourceBaseOrderService.getOne(Wrappers.<FiSourceBaseOrder>lambdaQuery()
                    .eq(FiSourceBaseOrder::getSource, baseOrder.getSource())
                    .eq(FiSourceBaseOrder::getSourceNo, baseOrder.getSourceNo())
                    .orderByDesc(FiSourceBaseOrder::getMaxSource)
                    .last("limit 1"));
            if(Objects.isNull(maxSourceOrder)) {
                baseOrder.setMaxSource(1);
            }else {
                baseOrder.setMaxSource(maxSourceOrder.getMaxSource() + 1);
            }
        }

        String flowNum = String.format("%03d", baseOrder.getMaxSource());
        baseOrder.setPurOrdNo(baseOrder.getSourceNo() + flowNum);

        List<FiSourceBaseOrderDetail> baseOrderDetails = new ArrayList<>();
        BigDecimal localNotaxDe = BigDecimal.ZERO;
        for(PmReceiptregB pmReceiptregB : pmReceiptregBS) {
            FiSourceBaseOrderDetail fiSourceBaseOrderDetail = new FiSourceBaseOrderDetail();
            localNotaxDe = localNotaxDe.add(new BigDecimal(FiSourceProjEstiPayableDetail.getLocalNotaxDe()));
            fiSourceBaseOrderDetail.setPkMain(baseOrder.getPkMain());
            fiSourceBaseOrderDetail.setPurOrdNo(baseOrder.getPurOrdNo());
            fiSourceBaseOrderDetail.setPurOrdItem(pmReceiptregB.getRowno());
            fiSourceBaseOrderDetail.setPrjCode(pmReceiptregB.getPkProject());
            fiSourceBaseOrderDetail.setItTotAmExTax(pmReceiptregB.getLocalNotaxDe());
            fiSourceBaseOrderDetail.setTaxrt(detail.getTaxrate());
            fiSourceBaseOrderDetail.setTaxAmount(detail.getLocalTaxDe());
            fiSourceBaseOrderDetail.setItTotAmIncTax(detail.getLocalMoneyDe());
            fiSourceBaseOrderDetail.setAmt(detail.getLocalMoneyDe());
            fiSourceBaseOrderDetail.setRecepId("否");
            fiSourceBaseOrderDetail.setPrvMkCode(prvMkCode);
            baseOrderDetails.add(fiSourceBaseOrderDetail);
        }
        fiSourceBasePaymentOrderVO.setLocalNotaxDe(localNotaxDe.toPlainString());
        baseOrder.setTotAmExTax(fiSourceBasePaymentOrderVO.getLocalNotaxDe());

        fiSourceBaseOrderService.save(baseOrder);
        fiSourceBaseOrderDetailService.saveBatch(baseOrderDetails);

        fiSourceBaseOrderService.pushAgain(baseOrder.getPkMain());
    }

    private void convertData(FiSourcePuPayable source, List<FiSourcePuPayableDetail> sourceDetails) {
        String pkPayablebill = source.getPkPayablebill();

        // 转换主表数据
        FiConvertPuPayable convert = new FiConvertPuPayable();
        apiCompareCache.convertData(source, convert, BillTypeEnum.PU_PAYABLE.getCode());

        // 处理已存在的转换数据
        FiConvertPuPayable existing = convertService.getOne(Wrappers.<FiConvertPuPayable>lambdaQuery()
                .eq(FiConvertPuPayable::getPkPayablebill, pkPayablebill));
        if (existing != null) {
            convert.setId(existing.getId());
            convertDetailService.remove(Wrappers.<FiConvertPuPayableDetail>lambdaQuery()
                    .eq(FiConvertPuPayableDetail::getPkPayablebill, pkPayablebill));
        }

        // 转换明细数据
        List<FiConvertPuPayableDetail> convertDetails = new ArrayList<>();
        for (FiSourcePuPayableDetail detail : sourceDetails) {
            FiConvertPuPayableDetail convertDetail = new FiConvertPuPayableDetail();
            apiCompareCache.convertData(detail, convertDetail, BillTypeEnum.PU_PAYABLE_DETAIL.getCode());
            convertDetail.setId(null);
            convertDetails.add(convertDetail);
        }

        // 保存转换后的数据
        convertService.saveOrUpdate(convert);
        convertDetailService.saveOrUpdateBatch(convertDetails);
    }

    private void pushToYgPlatform(FiSourcePuPayable source, YgInvoiceCheckContext context) {
        if (!ygConfig.getEnable()) {
            return;
        }

        // 使用已构建好的完整Context生成VO
        YgInvoiceCheckVO ygInvoiceCheckVO = ygInvoiceCheckVOBuilder.buildWithContext(context);
        ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000010.getServCode());

        Map<String, Object> data = new HashMap<>();
        data.put("list", Collections.singletonList(ygInvoiceCheckVO));
        log.info("【采购-发票校验申请服务】同步，同步参数为: {}", JSONObject.toJSONString(data));
        String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(data));
        // 对 result 的返回值进行判空处理
        if (StringUtils.isEmpty(result)) {
            throw new CheckedException("响应失败，响应为空。");
        }

        YgResult<List<YgResp>> ygResult = JSONObject.parseObject(result,
                new TypeReference<YgResult<List<YgResp>>>() {
                });

        // 首先判断外层响应是否成功
        if ("6100000".equals(ygResult.getCode()) || "6710300".equals(ygResult.getCode())) {
            // 检查data是否为空
            if (ygResult.getData() == null || CollectionUtils.isEmpty(ygResult.getData())) {
                throw new CheckedException("响应数据为空");
            }

            // 检查内层响应状态
            boolean hasError = false;
            StringBuilder errorMsg = new StringBuilder();

            for (YgResp resp : ygResult.getData()) {
                if (!"0".equals(resp.getCode())) {
                    hasError = true;
                    errorMsg.append("单据[").append(resp.getDisCode()).append("]处理失败：")
                            .append(resp.getMessage()).append("; ");
                }
            }

            if (hasError) {
                throw new CheckedException("部分单据处理失败: " + errorMsg);
            }

            // 所有检查通过，更新状态为成功
            updatePushSuccess(source);
        } else if ("6990399".equals(ygResult.getCode())) {
            throw new CheckedException("响应失败，响应结果: " + JSON.toJSONString(ygResult));
        } else {
            throw new CheckedException("未知响应，响应结果: " + JSON.toJSONString(ygResult));
        }
    }

    private void handleUnifiedException(FiSourcePuPayable source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setPushTime(DateUtil.formatDateTime(new Date()));
        source.setIntegrationStatus("1");

        // 根据异常信息判断失败类型
        PushStatusEnum pushStatus;
        if (pushMsg.contains("核销检查失败")) {
            pushStatus = PushStatusEnum.VERIFY_FAIL;
        } else if (pushMsg.contains("订单单号生成失败")) {
            pushStatus = PushStatusEnum.ORDER_NUMBER_FAIL;
        } else if (pushMsg.contains("订单同步失败")) {
            pushStatus = PushStatusEnum.ORDER_SYNC_FAIL;
        } else if (pushMsg.contains("转换失败")) {
            pushStatus = PushStatusEnum.CONVERT_FAIL;
        } else if (pushMsg.contains("推送失败")) {
            pushStatus = PushStatusEnum.PUSH_FAIL;
        } else {
            // 默认为推送失败
            pushStatus = PushStatusEnum.PUSH_FAIL;
        }

        source.setPushStatus(pushStatus.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        platformLogService.recordLog(
                source.getPkPayablebill(),
                source.getPkTradetype(),
                Constants.FAIL,
                pushMsg
        );
    }

    private void updatePushSuccess(FiSourcePuPayable source) {
        source.setPushTime(DateUtil.formatDateTime(new Date()));
        source.setIntegrationStatus("0");
        source.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
        source.setPushMsg("");
        source.setReadiness(1L);
        source.setReturnStatus("");
        source.setReturnMsg("");
        source.setReturnTime("");
        source.setReturnBill("");
        this.updateById(source);

        platformLogService.recordLog(
                source.getPkPayablebill(),
                source.getPkTradetype(),
                Constants.SUCCESS,
                ""
        );
    }

    @Override
    public Page<FiSourcePuPayableVO> beforeConvertDataPage(Page<FiSourcePuPayableVO> page, FiSourcePuPayableQueryDTO queryDTO) {
        Page<FiSourcePuPayableVO> resulte = baseMapper.beforeConvertDataPage(page, queryDTO);
        if (CollectionUtil.isNotEmpty(resulte.getRecords())){
            for (FiSourcePuPayableVO data : resulte.getRecords()){
                data.setReturnStatusName(ReturnStatusEnum.getByCode(data.getReturnStatus()).getName());
                if(StrUtil.isNotBlank(data.getPkTradetype())) {
                    if(data.getPkTradetype().equals("F1-Cxx-02")) {
                        data.setPkTradetypeName("采购应付单");
                    }else if(data.getPkTradetype().equals("F1-Cxx-01")) {
                        data.setPkTradetypeName("项目应付单");
                    }
                }
                FiSourcePuPayableDetail detail = detailService.getOne(Wrappers.<FiSourcePuPayableDetail>lambdaQuery()
                        .eq(FiSourcePuPayableDetail::getPkPayablebill, data.getPkPayablebill()).last("limit 1"));
                if(null != detail) {
                    data.setPkSupplierName(dmMapper.getSupplierName(detail.getSupplier()));
                    data.setBillcode(detail.getPurchaseorder());
                }
                ApiCompare apiCompare = apiCompareCache.getDictionaryValue(BillTypeEnum.PU_PAYABLE.getCode(), "billmaker", YgUtil.toStringTrim(data.getBillmaker()));
                data.setBillmakerName(Objects.isNull(apiCompare)?null:apiCompare.getTargetSystemName());
                ApiCompare apiCompare1 = apiCompareCache.getDictionaryValue(BillTypeEnum.PU_PAYABLE.getCode(), "pk_currtype", YgUtil.toStringTrim(data.getPkCurrtype()));
                data.setPkCurrtypeName(Objects.isNull(apiCompare1)?null:apiCompare1.getTargetSystemCode());
                if(StrUtil.isNotBlank(data.getDef47())) {
                    data.setDef47Name(dmMapper.getCosTypeNameByPk(data.getDef47()));
                }
            }
        }
        return resulte;
    }

    @Override
    public Page<FiSourcePuPayableDetailVO> getBeforeConvertDataPage(Page<FiSourcePuPayableDetailVO> page, FiSourcePuPayableDetailQueryDTO queryDTO) {
        return baseMapper.getBeforeConvertDataPage(page, queryDTO);
    }

    @Override
    public List<FiConvertPuPayableVO> getConvertList(String pkPayablebill) {
        List<FiConvertPuPayableVO> voList = new ArrayList<>();
        FiConvertPuPayable convert = convertService.getOne(
                Wrappers.<FiConvertPuPayable>lambdaQuery()
                        .eq(FiConvertPuPayable::getPkPayablebill, pkPayablebill));
        if (convert != null) {
            FiConvertPuPayableVO vo = new FiConvertPuPayableVO();
            BeanUtils.copyProperties(convert, vo);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public Page<FiConvertPuPayableDetailVO> getConvertData(Page<FiConvertPuPayableDetailVO> page, String pkPayablebill) {
        return baseMapper.getConvertData(page, pkPayablebill);
    }

    @Override
    public Map<String, FiSourcePuPayableDetailVO> getNoPostedBySrcBillIds(List<String> srcBillIds) {
        if (CollectionUtils.isEmpty(srcBillIds)) {
            return Collections.emptyMap();
        }
        return baseMapper.getNoPostedBySrcBillIds(srcBillIds);
    }

    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for (Long id : queryVO.getIdList()) {
            FiSourcePuPayable entity = this.getById(id);
            try {
                this.push(entity.getPkPayablebill());
            } catch (Exception e) {
                resultList.add(entity.getBillno() + e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
     }
}
