

package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.FiConvertBaseOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertBaseOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceBaseOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceBaseOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceBaseOrder;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceBaseOrderService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceBaseOrderQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/baseOrder" )
@Api(value = "baseOrder", tags = "订单源数据主表管理")
public class FiSourceBaseOrderController {

    private final  FiSourceBaseOrderService fiSourceBaseOrderService;

    /**
     * 报错重推
     *
     * @return
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{pk}")
    public R<Boolean> pushAgain(@PathVariable("pk") String pk) {
        return R.ok(fiSourceBaseOrderService.pushAgain(pk));
    }

    /**
     * 订单转换前分页查询
     *
     * @return
     */
    @ApiOperation(value = "订单转换前分页查询", notes = "订单转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourceBaseOrderDTO>> beforeConvertDataPage(Page page, FiSourceBaseOrderQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        return R.ok(fiSourceBaseOrderService.beforeConvertDataPage(page,queryVO));
    }

    /**
     * 转换前数据
     * @return
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage" )
    public R<Page<FiSourceBaseOrderDetailDTO>> getBeforeConvertDataPage(Page page, FiSourceBaseOrderQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getPkMain(), "主键不能为空");
        return R.ok(fiSourceBaseOrderService.getBeforeConvertDataPage(page,queryVO));
    }

    /**
     * 主体表转换后单条字段
     * @return
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList" )
    public R<List<FiConvertBaseOrderDTO>> getConvertList(String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceBaseOrderService.getConvertList(pk));
    }
    /**
     * 获取转换后详情分页数据
     * @return
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData" )
    public R<Page<FiConvertBaseOrderDetailDTO>> getConvertData(Page page, String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceBaseOrderService.getConvertData(page,pk));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiSourceBaseOrderService.update(Wrappers.<FiSourceBaseOrder>lambdaUpdate()
                .set(FiSourceBaseOrder::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourceBaseOrder::getPushMsg, "成功")
                .in(FiSourceBaseOrder::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourceBaseOrderService.pushAgainBatch(queryVO));
    }
}
