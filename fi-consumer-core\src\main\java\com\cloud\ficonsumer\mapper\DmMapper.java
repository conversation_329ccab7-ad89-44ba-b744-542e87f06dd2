package com.cloud.ficonsumer.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.cloud.ficonsumer.common.FiOrgConvert;
import com.cloud.ficonsumer.common.UCN;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.vo.*;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;
import java.util.Set;


@DS("erp")
@Mapper
public interface DmMapper {

    /**
     * 增加接收日志到日志记录表
     *
     * @return
     */
    @Insert("<script>INSERT INTO BD_PLATFORMLOG (PK_LOG,PK_BILL,PK_BILLTYPE,CODE,MESSAGE) VALUES (#{query.pkLog},#{query.pkBill},#{query.pkBillType},#{query.code},#{query.msg}) " +
            "</script>")
    int insertPlatFormLog(@Param("query") PlatFormLogVO platFormLogVO);

    /**
     * 更新接收日志到日志记录表
     *
     * @return
     */
    @Update("<script>UPDATE BD_PLATFORMLOG SET code=#{query.code},MESSAGE =#{query.msg} WHERE PK_LOG = #{query.pkLog} " +
            "</script>")
    int updatePlatFormLog(@Param("query") PlatFormLogVO platFormLogVO);

    /**
     * 根据物料主键获取物料信息
     *
     * @param pkMaterial
     * @return
     */
    @Select("<script>SELECT code,name " +
            " FROM bd_material " +
            " WHERE PK_MATERIAL = #{pkMaterial} </script>")
    MaterialVO getMaterialByPk(@Param("pkMaterial") String pkMaterial);

    /**
     * 根据组织主键获取组织信息
     *
     * @param pkOrg
     * @return
     */
    @Select("<script>SELECT DEF5 " +
            " FROM ORG_ORGS " +
            " WHERE PK_ORG = #{pkOrg} </script>")
    String getOrgByPk(@Param("pkOrg") String pkOrg);

    /**
     * 根据组织主键获取组织信息
     *
     * @param pkOrg
     * @return
     */
    @Select("<script>SELECT name " +
            " FROM ORG_ORGS " +
            " WHERE PK_ORG = #{pkOrg} </script>")
    String getOrgNameByPk(@Param("pkOrg") String pkOrg);

    /**
     * 根据部门主键查询部门信息
     *
     * @param pkDept 部门主键
     * @return 部门UCN
     */
    UCN getDeptByPK(@Param("pkDept") String pkDept);

    /**
     * 根据物料主键获取物料信息
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT PK_BILLTYPECODE " +
            " FROM bd_billtype " +
            " WHERE PK_BILLTYPEID = #{pk} </script>")
    String getCtrantypeIdByPk(@Param("pk") String pk);

    /**
     * 根据主键获取信息
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT def2 " +
            " FROM BD_SUPPLIER " +
            " WHERE PK_SUPPLIER = #{pk} </script>")
    String getSupplierByPk(String pk);

    /**
     * 根据主键获取供应商名称
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT name " +
            " FROM BD_SUPPLIER " +
            " WHERE PK_SUPPLIER = #{pk} </script>")
    String getSupplierName(String pk);

    /**
     * 根据主键获取信息
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT def2 " +
            " FROM BD_CUSTOMER " +
            " WHERE PK_CUSTOMER = #{pk} </script>")
    String getCustomerByPk(String pk);

    /**
     * 根据主键获取信息
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT ACCNUM " +
            " FROM BD_BANKACCSUB " +
            " WHERE PK_BANKACCSUB = #{pk} </script>")
    String getAccNumByPk(String pk);

    /**
     * 根据主键获取信息
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT CODE " +
            " FROM bd_defdoc " +
            " WHERE PK_DEFDOC = #{pk} </script>")
    String getCosTypeByPk(String pk);

    /**
     * 根据主键获取平台业务事项名称
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT NAME " +
            " FROM bd_defdoc " +
            " WHERE PK_DEFDOC = #{pk} </script>")
    String getCosTypeNameByPk(String pk);

    /**
     * 根据主当前业务事项主键查询上级
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT T1.innercode AS innercode1,T1.pid AS PID1,T1.CODE AS CODE1, T1.NAME AS NAME1," +
            " T2.innercode AS innercode2,T2.pid AS PID2,T2.CODE AS CODE2, T2.NAME AS NAME2, " +
            " T3.innercode AS innercode3,T3.pid AS PID3,T3.CODE AS CODE3, T3.NAME AS NAME3 " +
            " FROM bd_defdoc T1 " +
            " LEFT JOIN bd_defdoc T2 ON T1.PID =T2.pk_defdoc " +
            " LEFT JOIN bd_defdoc T3 ON T2.PID =T3.pk_defdoc " +
            " WHERE T1.pk_defdoc = #{pk} </script>")
    CosTypeVO getCosTypeVOByCurr(String pk);

    /**
     * 根据主键获取信息
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT NAME " +
            " FROM bd_defdoc " +
            " WHERE PK_DEFDOC = #{pk} </script>")
    String getPayAmoByPk(String pk);

    /**
     * 根据主键获取信息
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT CODE " +
            " FROM bd_defdoc " +
            " WHERE PK_DEFDOC = #{pk} </script>")
    String getDeptTypeByPk(String pk);

    /**
     * 根据主键获取信息
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT VALUE " +
            " FROM pub_sysinit " +
            " WHERE initcode='PT02' AND pk_org = #{pk} </script>")
    String getIsAcrByPk(String pk);

    /**
     * 根据主键获取信息
     *
     * @param pk
     * @return
     */
    @Select("<script>SELECT project_code " +
            " FROM bd_project " +
            " WHERE pk_project = #{pk} </script>")
    String getProjectByPk(String pk);

    @Select("<script> SELECT org_orgs.def5 " +
            " FROM org_costregion " +
            " LEFT JOIN org_orgs ON org_orgs.pk_org = org_costregion.pk_org " +
            " WHERE org_costregion.pk_costregion = #{pk} </script>")
    String getOrgCostregionByPk(String pk);

    @Select("<script> SELECT org_orgs.def5 " +
            " FROM org_stockorg_v " +
            " LEFT JOIN org_orgs ON org_orgs.pk_org = org_stockorg_v.pk_org " +
            " WHERE org_stockorg_v.pk_org = #{pk} </script>")
    String getPkStockOrgByPk(String pk);

    @Select("<script> SELECT code " +
            " FROM bd_stordoc " +
            " where pk_stordoc = #{pk} </script>")
    String getPkStockByPk(String pk);

    /**
     * 查询项目主数据是否准备好
     *
     * @param pkProject
     * @return
     */
    @Select("<script>select pk_log from bd_platformlog where pk_bill=#{pkProject} and pk_billtype='bd_project' and code='000000' " +
            "</script>")
    String getProjectReadiness(@Param("pkProject") String pkProject);

    /**
     * 查询增量的订单数据
     * " and ctrantypeid in ('21-Cxx-001','21-Cxx-N01','21-Cxx-N02','21-Cxx-N03','21-Cxx-N05','21-Cxx-N06','21-Cxx-007','21-Cxx-015','21-Cxx-018') " +
     * @return
     */
    @Select("<script>SELECT * FROM (SELECT * FROM po_order WHERE DR = 0 " +
            " and (vdef39!='1' or vdef39 is null) and forderstatus = 3  " +
            " and bislatest='Y' " +
            "<if test=\"query.pkOrder !=null and query.pkOrder !=''\"> " +
            " and pk_order = #{query.pkOrder} " +
            "</if> " +
            "<if test=\"query.vbillcode !=null and query.vbillcode !=''\"> " +
            " and vbillCode = #{query.vbillcode} " +
            "</if> " +
            " and vtrantypecode in ('21-Cxx-001','21-Cxx-N01') " +
            "<if test=\"query.grabTime !=null and query.grabTime !=''\"> " +
            " and taudittime >= #{query.grabTime} " +
            "</if> " +
            " and pk_org in (SELECT TT.pk_org " +
            "   FROM pub_sysinit TT " +
            "   WHERE TT. initcode='PT03' AND TT.VALUE='Y') " +
            " ORDER BY TS) WHERE ROWNUM &lt;= 10000 </script>")
    List<FiSourceOrderVO> getOrderMainList(@Param("query") FiSourceOrderDTO fiSourceOrderDTO);

    /**
     * 根据订单查询详情接口
     *
     * @param pkOrder
     * @return
     */
    @Select("<script>SELECT T1.*,T2.CODE AS MATERIAL_CODE,T2.NAME " +
            " FROM po_order_b T1 " +
            " LEFT JOIN bd_material T2 ON T1.PK_MATERIAL=T2.PK_MATERIAL " +
            " WHERE T1.DR = 0 and T1.PK_ORDER = #{pkOrder} </script>")
    List<FiSourceOrderDetailVO> getOrderDetailList(@Param("pkOrder") String pkOrder);

    /**
     * 更新订单接收状态
     *
     * @return
     */
    @Update("<script>UPDATE po_order SET vdef39='1' WHERE pk_order=#{pkOrder} " +
            "</script>")
    void updateOrderReceive(@Param("pkOrder") String pkOrder);

    /**
     * 查询增量的通用报销单数据
     * pk_tradetype=F1-Cxx-06
     * DEF20
     * 通用报销单(报账)  其他应付单
     * bd_defdoc 的 def1（不传中台标识）给了‘N’代表不传
     * @return
     */
    @Select("<script>SELECT * FROM (SELECT * FROM ap_payablebill WHERE DR = 0 and pk_tradetype in ('F1-Cxx-06','F1-Cxx-03') " +
            " and (def24!='1' or def24 is null) AND BILLSTATUS IN (1, 8) " +
            "<if test=\"query.pkPayablebill !=null and query.pkPayablebill !=''\"> " +
            " and PK_PAYABLEBILL = #{query.pkPayablebill} " +
            "</if> " +
            "<if test=\"query.billno !=null and query.billno !=''\"> " +
            " and billno = #{query.billno} " +
            "</if> " +
            " and def47 in (SELECT bd_defdoc.PK_DEFDOC  " +
            "   FROM bd_defdoc " +
            "   LEFT JOIN bd_defdoclist ON bd_defdoclist.pk_defdoclist = bd_defdoc.PK_DEFDOCLIST  " +
            "   WHERE innercode NOT LIKE CONCAT(#{query.innercode},'%') AND bd_defdoclist.code='ptywsx'" +
            "   and bd_defdoc.def1!='N' ) " +
            " and pk_org in (SELECT pk_org " +
            "   FROM pub_sysinit " +
            "   WHERE  initcode='PT01' AND VALUE='Y') " +
            " ORDER BY TS) WHERE ROWNUM &lt;= 10000 </script>")
    List<FiSourceReimbursementVO> getReimbursementMainList(@Param("query") FiSourceReimbursementDTO fiSourceReimbursementDTO);

    /**
     * 根据通用报销单查询详情接口
     *
     * @return
     */
    @Select("<script>SELECT * FROM ap_payableitem WHERE DR = 0 and PK_PAYABLEBILL =#{pkPayablebill} </script>")
    List<FiSourceReimbursementDetailVO> getReimbursementDetailList(@Param("pkPayablebill") String pkPayablebill);

    /**
     * 更新通用报销单接收状态
     *
     * @return
     */
    @Update("<script>UPDATE ap_payablebill SET def24='1' WHERE PK_PAYABLEBILL=#{pkPayablebill} " +
            "</script>")
    void updateReimbursementReceive(@Param("pkPayablebill") String pkPayablebill);

    /**
     * 更新通用报销单/付款单
     *
     * @return
     */
    @Update("<script>UPDATE ap_payablebill SET def53=#{query.returnStatus} ,def81=#{query.returnTime}" +
            "<if test=\"query.successMsg !=null and query.successMsg !=''\"> " +
            " ,def82=#{query.returnMsg} " +
            "</if> " +
            "<if test=\"query.returnMsg !=null and query.returnMsg !=''\"> " +
            " ,def54=#{query.returnMsg} " +
            "</if> " +
            " WHERE billno=#{query.billno} AND DR=0 AND pk_group='0001A110000000000CE4'" +
            "</script>")
    void updatePayablebillStatus(@Param("query") FiSourceCalBackDTO fiSourceCalBackDTO);

    /**
     * 更新应付单
     *
     * @return
     */
    @Update("<script>UPDATE ap_paybill SET def53=#{query.returnStatus} ,def81=#{query.returnTime}" +
            "<if test=\"query.successMsg !=null and query.successMsg !=''\"> " +
            " ,def82=#{query.returnMsg} " +
            "</if> " +
            "<if test=\"query.returnMsg !=null and query.successsMsg !=''\"> " +
            " ,def54=#{query.returnMsg} " +
            "</if> " +
            " WHERE billno=#{query.billno} AND DR=0 AND pk_group='0001A110000000000CE4'" +
            "</script>")
    void updatePaybillStatus(@Param("query") FiSourceCalBackDTO fiSourceCalBackDTO);

    /**
     * 更新凭证是否拉取
     *
     * @return
     */
    @Update("<script>UPDATE bd_voucherresult SET " +
            " code=#{query.code},profcenter=#{query.profcenter},message=#{query.message},sapdocid=#{query.sapdocid}" +
            ",year=#{query.year},company=#{query.company},def2=#{query.findocid} " +
            " WHERE docuniqueid=#{query.docuniqueid} " +
            "</script>")
    void updateAccounting(@Param("query") FiVoucherCalBackDTO query);

    /**
     * 查询增量的项目应收数据（承包业务）
     *
     * @return
     */
    @Select("<script>SELECT * FROM (SELECT * FROM ar_recbill WHERE DR = 0 and pk_tradetype in ('F0-Cxx-01','F0-Cxx-02') " +
            " and def24!='1' AND BILLSTATUS IN (1, 8) " +
            "<if test=\"query.pkRecbill !=null and query.pkRecbill !=''\"> " +
            " and PK_RECBILL = #{query.pkRecbill} " +
            "</if> " +
            " and pk_org in (SELECT pk_org " +
            "   FROM pub_sysinit " +
            "   WHERE  initcode='PT03' AND VALUE='Y') " +
            " ORDER BY TS) WHERE ROWNUM &lt;= 10000 </script>")
    List<FiSourceReceivableVO> getReceivableMainList(@Param("query") FiSourceReceivableDTO fiSourceReceivableDTO);

    /**
     * 根据项目应收查询详情接口（承包业务）
     *
     * @return
     */
    @Select("<script>SELECT * FROM ar_recitem WHERE DR = 0 and PK_RECBILL =#{pkRecbill} </script>")
    List<FiSourceReceivableDetailVO> getReceivableDetailList(@Param("pkRecbill") String pkRecbill);

    /**
     * 更新项目应收接收状态（承包业务）
     *
     * @return
     */
    @Update("<script>UPDATE ar_recbill SET def24='1' WHERE PK_RECBILL=#{pkRecbill} " +
            "</script>")
    void updateReceivableReceive(@Param("pkRecbill") String pkRecbill);

    /**
     * 查询增量的采购入库
     * @param fiSourceStoreDTO
     * @return
     */
    @Select("<script>SELECT * FROM (select distinct T1.cbillid " +
            "  from ia_i2bill T1 " +
            "  left join ia_i2bill_b T2 on T1.cbillid = T2.cbillid " +
            "  left join org_costregion T3 on T3.pk_costregion = T1.pk_org" +
            "  left join po_order T4 on T4.pk_order = T2.cfirstid" +
            " where T2.vfirsttype = '21' and T1.dr = 0 and T1.vdef12!='1' " +
            " and T4.vdef39='1' " +
            "<if test=\"query.cbillid !=null and query.cbillid !=''\"> " +
            " and T1.cbillid = #{query.cbillid} " +
            "</if> " +
            "<if test=\"query.vbillcode !=null and query.vbillcode !=''\"> " +
            " and T1.vbillcode = #{query.vbillcode} " +
            "</if> " +
            "<if test=\"query.grabTime !=null and query.grabTime !=''\"> " +
            " and T1.creationtime >= #{query.grabTime} " +
            "</if> " +
            " and T3.pk_org in (SELECT pk_org " +
            "   FROM pub_sysinit " +
            "   WHERE  initcode='PT03' AND VALUE='Y') " +
            " ) WHERE ROWNUM &lt;= 10000 </script>")
    List<FiSourceStoreVO> getStoreMainList(@Param("query") FiSourceStoreDTO fiSourceStoreDTO);

    /**
     * 根据主键查询采购入库主表
     * @return
     */
    @Select("<script>SELECT T1.cbillid,T1.vbillcode,T3.pk_org,T1.cvendorid,T1.billmaker,T1.dmakedate, " +
            "T1.cstockorgvid,T1.cstordocmanid,T1.cpsnid,T1.vnote,T1.cdeptvid,T1.bestimateflag " +
            "FROM ia_i2bill T1 " +
            "left join org_costregion T3 on T3.pk_costregion = T1.pk_org " +
            "where T1.cbillid =#{cbillid} </script>")
    FiSourceStoreVO getStoreMain(@Param("cbillid") String cbillid);

    /**
     * 根据主键查详情
     * @param cbillid
     * @return
     */
    /**
     * 根据项目应收查询详情接口（承包业务）
     *
     * @return
     */
    @Select("<script>select ia_i2bill_b.cbillid, " +
            "        po_order_b.vcontractcode, " +
            "        ia_i2bill_b.cbill_bid, " +
            "        ia_i2bill_b.vfirstcode AS vbillcode, " +
            "        ia_i2bill_b.vfirstrowno AS crownoOrder, " +
            "        ia_i2bill_b.nnum, " +
            "        po_order_b.nqtorigtaxprice, " +
            "        ia_i2bill_b.nprice, " +
            "        po_order_b.ntaxrate, " +
            "        po_order_b.ntax, " +
            "        po_order_b.norigtaxmny, " +
            "        ia_i2bill_b.nmny, " +
            "        ia_i2bill_b.vbatchcode, " +
            "        bd_project.project_code AS cprojectid, " +
            "        ia_i2bill_b.cinventoryvid, " +
            "        ia_i2bill_b.vsrctype, " +
            "        ia_i2bill_b.vbdef7, " +
            "        ia_i2bill_b.crowno " +
            "   from ia_i2bill_b " +
            "   left join po_order_b  on ia_i2bill_b.cfirstbid = po_order_b.pk_order_b " +
            "   left join bd_project on bd_project.pk_project = ia_i2bill_b.cprojectid " +
            "   where ia_i2bill_b.dr = 0 and  ia_i2bill_b.vfirsttype = '21' and ia_i2bill_b.cbillid =#{cbillid} </script>")
    List<FiSourceStoreDetailVO> getStoreDetailList(@Param("cbillid") String cbillid);

    /**
     * 更新为已拉取
     *
     * @return
     */
    @Update("<script>UPDATE ia_i2bill SET vdef12='1' WHERE cbillid=#{cbillid} " +
            "</script>")
    void updateStoreReceive(@Param("cbillid") String cbillid);

    @Select("<script>SELECT distinct * FROM (SELECT  " +
            "gv.pk_voucher , " +
            "gv.num, " +
            "'4' as pk_vouchertype ,  " +
            "gv.pk_org , " +
            "gv.free5 , " +
            "null as explanation , " +
            "gv.prepareddate , " +
            "gv.tallydate , " +
            "gv.period, " +
            "gv.pk_prepared, " +
            "gv.pk_checked, " +
            "'RMB' as pk_currtype, " +
            "gv.attachment, " +
            "gv.year, " +
            "case when gv.adjustperiod = '~' then null else gv.adjustperiod end as adjustperiod, " +
            "case when gv.offervoucher = '~' then '1' else '0' end as offervoucher " +
            "FROM GL_VOUCHER gv  " +
            "LEFT JOIN (select distinct pk_currtype,pk_voucher from GL_DETAIL where dr = 0) gd  " +
            "ON gv.PK_VOUCHER = gd.PK_VOUCHER " +
            "LEFT JOIN bd_voucherresult bv ON gv.PK_VOUCHER = bv.pk_voucher AND bv.dr = 0 " +
            "WHERE gv.DR = 0 and gv.pk_system = 'IA' and pk_manager != 'N/A' " +
            "AND bv.pk_voucher IS null " +
            " and gv.pk_org in (SELECT pk_org " +
            "   FROM pub_sysinit " +
            "   WHERE  initcode='PT03' AND VALUE='Y') " +
            "<if test=\"pkVoucher !=null and pkVoucher !=''\"> " +
            " and gv.pk_voucher = #{pkVoucher} " +
            "</if> " +
            " ) WHERE ROWNUM &lt;= 10000 </script>")
    List<FiSourceAccountingVO>
    getAccountingMainList(@Param("pkVoucher") String pkVoucher);

    /**
     * 根据项目应收查询详情接口（承包业务）
     * @return
     */
    @Select("SELECT  " +
            "gv.pk_voucher , " +
            "gv.num, " +
            "gd.accountcode as pk_accasoa, " +
            "gd.pk_detail," +
            "gd.detailindex as flxh, " +
            "case when gd.localdebitamount != 0 then 'D' else 'C' end as crde, " +
            "case when localdebitamount is null then localdebitamount else localdebitamount end AS amount, " +
            "NULL AS busidate, " +
            "gd2.assid  " +
            "FROM GL_VOUCHER gv  " +
            "LEFT JOIN GL_DETAIL gd  " +
            "ON gv.PK_VOUCHER = gd.PK_VOUCHER AND gd.DR = 0 " +
            "LEFT JOIN GL_DOCFREE1 gd2 ON gd.assid = gd2.assid AND gd2.dr = 0 " +
            "WHERE gv.DR = 0 and pk_manager != 'N/A'" +
            "and gv.pk_voucher = #{pkVoucher}")
    List<FiSourceAccountingDetailVO> getAccountingDetailList(@Param("pkVoucher") String pkVoucher);

    @Select("SELECT project_code " +
            "FROM gl_docfree1 gd  " +
            "LEFT JOIN bd_project bp ON gd.f10 = bp.pk_project AND bp.dr = 0 " +
            "WHERE gd.dr = 0 AND gd.assid = #{assid}")
    String getAccountingProjectCode(@Param("assid")String assid);

    @Select("select name from bd_accasoa where dispname like '%********%' and pk_accasoa = #{pkAccasoa}")
    String getRgflxc(@Param("pkAccasoa")String pkAccasoa);

    @Select("select free5 from gl_voucher where pk_voucher = #{pkVoucher}")
    String getLrzx(@Param("pkVoucher")String pkVoucher);


    @Select("select f8 from gl_docfree1 where assid = #{assid}")
    String getAccountingInoutbusiclass(@Param("assid")String assid);

    @Select("select accountcode from gl_detail where pk_detail = #{pkDetail}")
    String getAccountingCode(@Param("pkDetail")String pkDetail);

    @Select("SELECT def2 " +
            "FROM gl_docfree1 gd  " +
            "LEFT JOIN bd_customer bc ON gd.f13 = bc.pk_customer AND bc.dr = 0 " +
            "WHERE gd.dr = 0 AND gd.assid = #{assid}")
    String getAccountingZzjg(@Param("assid")String assid);

    /**
     * 根据主键查询会计凭证主表
     * @return
     */
    @Select("<script>select * from ( SELECT  " +
            "gd.explanation " +
            "FROM GL_VOUCHER gv  " +
            "LEFT JOIN GL_DETAIL gd  " +
            "ON gv.PK_VOUCHER = gd.PK_VOUCHER AND gd.DR = 0 and pk_system = 'IA' " +
            "WHERE gv.DR = 0 and pk_manager != 'N/A'" +
            " and gv.pk_voucher = #{pkVoucher}" +
            "order by gd.detailindex)" +
            "where ROWNUM = 1" +
            "</script>")
    String getSAccountingExplanMain(@Param("pkVoucher") String pkVoucher);

    /**
     * 获取传智慧共享平台ERP组织
     *
     * @return 传智慧共享平台ERP组织
     */
    List<FiOrgConvert> listAllPushEnableOrg();

    /**
     * 查询ERP集成日志
     *
     * @param pkBill     单据主键
     * @param pkBillType 单据类型
     * @return 集成日志
     */
    List<PlatFormLogVO> selectPlatFormLog(@Param("pkBill") String pkBill,
                                          @Param("pkBillType") String pkBillType);

    /**
     * 查询 UDS 结果
     *
     * @param pkBill     业务单据主键
     * @param pkBillType 业务单据类型
     * @return UDS结果
     */
    List<BdUdsResult> listUdsResult(@Param("pkBill") String pkBill, @Param("pkBillType") String pkBillType);

    /**
     * 根据用户编码获取部门
     *
     * @param userCode 用户编码
     * @return 部门UCN
     */
    List<UCN> getDeptByUserCode(@Param("userCode") String userCode);

    /**
     * 【采购应付-发票校验申请服务】
     */
    List<FiSourcePuPayableVO> listPuPayable(@Param("query") FiSourcePuPayableQueryDTO queryDTO);

    /**
     * 【项目应付-发票校验申请服务】
     */
    List<FiSourcePuPayableVO> listPrjPayable(@Param("query") FiSourcePuPayableQueryDTO queryDTO);

    List<FiSourcePuPayableDetailVO> getPuPayableDetails(@Param("pkPayablebill") String pkPayablebill);

    void updatePuPayableReceive(@Param("pkPayablebill") String pkPayablebill);

    /**
     * 更新采购应付单为自建项目状态
     *
     * @param pkPayablebill 应付单标识
     */
    void updatePuPayableSelfBuild(@Param("pkPayablebill") String pkPayablebill);

    /**
     * 【采购-预付付款申请单接收服务 (结算对照)】
     * @param queryDTO 查询参数
     * @return 付款单列表
     */
    List<FiSourcePuPaybillVO> listPuPaybill(@Param("query") FiSourcePuPaybillQueryDTO queryDTO);
    /**
     * 【项目-预付付款申请单】
     */
    List<FiSourcePuPaybillVO> listPuPaybillXM(@Param("query") FiSourcePuPaybillQueryDTO queryDTO);

    List<FiSourcePuPaybillDetailVO> getPuPaybillDetails(@Param("pkSettlement") String pkSettlement);

    void updatePuPaybillReceive(@Param("billCode") String billCode);

    void updatePuPaybillSelfBuild(@Param("billCode") String billCode);

    /**
     * 【项目分包-项目暂估应付单】
     */
    List<FiSourceProjEstiPayableVO> listProjEstiPayable();

    List<FiSourceProjEstiPayableDetailVO> getProjEstiPayableDetails(@Param("pkEstipayablebill") String pkEstipayablebill);

    void updateProjEstiPayableReceive(@Param("pkEstipayablebill") String pkEstipayablebill);

    /**
     * 【项目分包-接收通用报账单服务】
     */
    List<FiSourceProjPaybillVO> listProjPaybill();

    List<FiSourceProjPaybillDetailVO> getProjPaybillDetails(@Param("pkSettlement") String pkSettlement);

    void updateProjPaybillReceive(@Param("pkSettlement") String pkSettlement);

    /**
     * 【项目分包-付款申请】
     */
    List<FiSourceProjsubPayableVO> listProjsubPayable();

    List<FiSourceProjsubPayableDetailVO> getProjsubPayableDetails(@Param("pkPayablebill") String pkPayablebill);

    void updateProjsubPayableReceive(@Param("pkPayablebill") String pkPayablebill);

    /**
     * 【销售订单接口-接收销售订单信息】
     */
    List<FiSourceSaleOrderVO> listSaleOrder();

    List<FiSourceSaleOrderDetailVO> getSaleOrderDetails(@Param("csaleorderid") String csaleorderid);

    void updateSaleOrderReceive(@Param("csaleorderid") String csaleorderid);

    /**
     * 根据编码查询主键值
     *
     * @return
     */
    @Select("<script>select PK_ORG from org_orgs where CODE=#{departCode} </script>")
    String getPkOrgByCode(String departCode);

    @Select("<script>SELECT * FROM (SELECT * FROM pm_feebalance WHERE DR = 0 " +
            " and (def8!='1' or def8 is null) and bill_status = 1  " +
            "<if test=\"query.pkFeebalance !=null and query.pkFeebalance !=''\"> " +
            " and pk_feebalance = #{query.pkFeebalance} " +
            "</if> " +
            "<if test=\"query.billCode !=null and query.billCode !=''\"> " +
            " and bill_code = #{query.billCode} " +
            "</if> " +
            " and pk_org in (SELECT TT.pk_org " +
            "   FROM pub_sysinit TT " +
            "   WHERE TT. initcode='PT03' AND TT.VALUE='Y') " +
            " ORDER BY TS) WHERE ROWNUM &lt;= 10000 </script>")
    List<FiSourceCostOrderVO> getCostOrderMainList(@Param("query") FiSourceCostOrderDTO fiSourceCostOrderDTO);

    @Select("<script>SELECT T1.*,T2.CODE AS MATERIAL_CODE,T2.NAME " +
            " FROM pm_feebalance_b T1 " +
            " LEFT JOIN bd_material T2 ON T1.PK_MATERIAL=T2.PK_MATERIAL " +
            " WHERE T1.DR = 0 and T1.pk_feebalance = #{pkFeebalance} </script>")
    List<FiSourceCostOrderDetailVO> getCostOrderDetailList(@Param("pkFeebalance") String pkFeebalance);

    @Update("<script>UPDATE pm_feebalance SET def8='1' WHERE pk_feebalance=#{pkFeebalance} " +
            "</script>")
    void updateCostOrderReceive(@Param("pkFeebalance") String pkFeebalance);

    @Select("<script>SELECT * FROM (SELECT * FROM pm_contr t1 " +
            " left join bd_project t2 on t1.pk_project = t2.pk_project " +
            " WHERE t1.DR = 0 AND t1.bill_status = 9 " +
            " and (t1.hdef44!='1' or t1.hdef44 is null) AND t1.bill_status = 9 " +
            " and t1.transi_type in ('4D42-01')  " +
            " and t2.pk_eps in (select pk_eps from pm_eps where eps_code not like '03%' and dr=0)  " +
            "<if test=\"query.pkContr !=null and query.pkContr !=''\"> " +
            " and t1.pk_contr = #{query.pkContr} " +
            "</if> " +
            "<if test=\"query.billCode !=null and query.billCode !=''\"> " +
            " and t1.bill_code = #{query.billCode} " +
            "</if> " +
            " and t1.pk_org in (SELECT TT.pk_org " +
            "   FROM pub_sysinit TT " +
            "   WHERE TT. initcode='PT03' AND TT.VALUE='Y') " +
            " ORDER BY t1.TS) WHERE ROWNUM &lt;= 10000 </script>")
    List<FiSourceVirtualOrderVO> getVirtualOrderMainList(@Param("query") FiSourceVirtualOrderDTO fiSourceVirtualOrderDTO);

    @Select("<script>SELECT T1.*,T2.CODE AS MATERIAL_CODE,T2.NAME " +
            " FROM pm_contr_works T1 " +
            " LEFT JOIN bd_material T2 ON T1.PK_MATERIAL=T2.PK_MATERIAL " +
            " WHERE T1.DR = 0 and T1.pk_contr = #{pkContr} </script>")
    List<FiSourceVirtualOrderDetailVO> getVirtualOrderDetailList(@Param("pkContr") String pkContr);

    @Update("<script>UPDATE pm_contr SET hdef44='1' WHERE pk_contr=#{pkContr} " +
            "</script>")
    void updateVirtualOrderReceive(@Param("pkContr") String pkContr);

//    @Select("<script>SELECT * " +
//            "FROM ap_paybill " +
//            "WHERE pk_paybill IN (select distinct T1.pk_paybill " +
//            "  from ap_paybill T1 " +
//            "  inner join ap_payitem T2 on T1.pk_paybill = T2.pk_paybill and T2.dr = 0 " +
//            "  inner join pm_contr T3 on T2.src_billid = T3.pk_contr " +
//            "  inner join bd_project T4 on T2.project = T4.pk_project " +
//            "  where T2.prepay = 1 and T1.pk_tradetype = 'F3-Cxx-01' and T1.dr = 0 and T1.billstatus in (1) " +
//            " and (T3.hdef44!='1' or T3.hdef44 is null) AND T3.bill_status = 9 " +
//            " and T3.transi_type in ('4D42-01')  " +
//            "<if test=\"query.pkContr !=null and query.pkContr !=''\"> " +
//            " and T3.pk_contr = #{query.pkContr} " +
//            "</if> " +
//            "<if test=\"query.billCode !=null and query.billCode !=''\"> " +
//            " and T3.bill_code = #{query.billCode} " +
//            "</if> " +
//            " and T3.pk_org in (SELECT TT.pk_org " +
//            "   FROM pub_sysinit TT " +
//            "   WHERE TT. initcode='PT03' AND TT.VALUE='Y') " +
//            "  and T4.pk_eps in (select pk_eps from pm_eps where eps_code not like '03%' and dr=0)) " +
//            "  and ROWNUM <= 10000 ORDER BY ts</script>")
//    List<FiSourceBaseOrderVO> getPaymentVirtualOrderMainList(@Param("query") FiSourceBaseOrderDTO fiSourceBaseOrderDTO);

    @Select("<script>SELECT * " +
            " FROM ap_payitem T2 " +
            " where T2.prepay = 1 and T2.dr = 0 " +
            " AND T2.pk_paybill = #{pkPaybill}</script>")
    List<FiSourceBaseOrderDetailVO> getPaymentBaseOrderDetailList(@Param("pkPaybill") String pkPaybill);

    @Update("<script>UPDATE ap_paybill SET def83='1' WHERE pk_paybill=#{pkPaybill} " +
            "</script>")
    List<FiSourceBaseOrderDetailVO> updateBaseOrderReceive(@Param("pkPaybill") String pkPaybill);

    /**
     * 根据订单明细主键批量查询订单明细信息
     *
     * @param pkOrderBList 订单明细主键列表
     * @return 订单明细信息列表
     */
    List<PoOrderB> listPoOrderBByPkB(@Param("pkOrderBList") List<String> pkOrderBList);

    /**
     * 根据入库单表体主键批量查询入库单明细信息
     *
     * @param cfirstbids 入库单表体主键列表
     * @return 入库单明细信息列表
     */
    List<IaI2billB> listIaI2billBByBids(@Param("cfirstbids") List<String> cfirstbids);

    @Select("<script> SELECT code " +
            " FROM bd_bankdoc " +
            " where pk_bankdoc = #{id} AND DR =0 </script>")
    String getBankCode(@Param("id") String value);

    @Select("<script> SELECT PK_BILLTYPECODE FROM BD_BILLTYPE WHERE PK_BILLTYPEID IN (SELECT ctrantypeid " +
            " FROM po_order " +
            " where VBILLCODE = #{id} AND DR =0) </script>")
    String getBillType(@Param("id")String value);

    @Insert("<script>INSERT INTO bd_voucherresult (pk_voucher,code,profcenter,docuniqueid,sapdocid,company,year,def1)" +
            " VALUES (#{vo.pkVoucher},0,#{vo.free5},#{vo.pkVoucher},#{vo.num},#{vo.pkOrg},#{vo.year},0) " +
            "</script>")
    void insertBdVoucherresult(@Param("vo") FiSourceAccountingVO fiSourceAccountingVO);


    /**
     * 批量查询应付单状态
     */
    @MapKey("id")
    Map<String, BillStatusDTO> batchGetApPayablebillStatus(@Param("billIds") List<String> billIds, @Param("validStatuses") List<String> validStatuses);

    /**
     * 批量查询采购发票状态
     */
    @MapKey("id")
    Map<String, BillStatusDTO> batchGetPoInvoiceStatus(@Param("billIds") List<String> billIds, @Param("validStatuses") List<String> validStatuses);

    /**
     * 批量查询收票登记状态
     */
    @MapKey("id")
    Map<String, BillStatusDTO> batchGetPmReceiptregStatus(@Param("billIds") List<String> billIds, @Param("validStatuses") List<String> validStatuses);

//    @Select("<script> SELECT T6.src_billid FROM CMP_SETTLEMENT T1 " +
//            " INNER JOIN AP_PAYBILL T2 ON T1.PK_BUSIBILL = T2.PK_PAYBILL " +
//            " LEFT JOIN AP_PAYABLEBILL T5 ON T2.BILLNO = T5.BILLNO " +
//            " LEFT JOIN AP_PAYABLEITEM T6 ON T6.PK_PAYABLEBILL = T5.PK_PAYABLEBILL " +
//            " WHERE T1.PK_SETTLEMENT = #{pkSettlement} AND T1.DR =0 AND ROWNUM = 1 </script>")
//    String selectSrcBillid(@Param("pkSettlement") String pkSettlement);
//
//    @Select("<script> SELECT T5.def53 FROM CMP_SETTLEMENT T1 " +
//            " INNER JOIN AP_PAYBILL T2 ON T1.PK_BUSIBILL = T2.PK_PAYBILL " +
//            " LEFT JOIN AP_PAYABLEBILL T5 ON T2.BILLNO = T5.BILLNO " +
//            " WHERE T1.PK_SETTLEMENT = #{pkSettlement} AND T1.DR =0 AND ROWNUM = 1 </script>")
//    String selectDef53(@Param("pkSettlement")String pkSettlement);
//
//    @Select("<script> SELECT T6.VBILLCODE FROM AP_PAYITEM T4 " +
//            " LEFT JOIN PO_ORDER T6 ON T6.VBILLCODE = T4.PURCHASEORDER " +
//            " WHERE T4.BILLNO = #{billno} AND T4.DR =0  AND ROWNUM = 1 </script>")
//    String selectVbillcode(@Param("billno")String billno);
//
//    @Select("<script> SELECT T7.BILL_CODE FROM AP_PAYITEM T4 " +
//            " LEFT JOIN PM_CONTR T7 ON T7.PK_PROJECT = T4.PROJECT " +
//            " WHERE T4.BILLNO = #{billno} AND T4.DR =0 AND ROWNUM = 1 </script>")
//    String selectBillcodeFromPmcontr(@Param("billno")String billno);
//
//    @Select("<script> SELECT T7.BILL_CODE FROM AP_PAYBILL T1 " +
//            " LEFT JOIN AP_PAYITEM T4 ON T1.PK_PAYBILL = T4.PK_PAYBILL " +
//            " LEFT JOIN PM_FEEBALANC T7 ON T7.pk_feebalance = T4.src_billid " +
//            " WHERE T1.PK_PAYBILL = #{pkPaybill} AND T1.DR =0 AND ROWNUM = 1 </script>")
//    String selectBillcodeFromPmfeeblance(@Param("pkPaybill")String pkPaybill);

    /**
     * 检查是否存在自建项目
     * 通过项目主键列表查询项目表(BD_PROJECT)和项目群表(PM_EPS)
     * 判断项目是否为自建项目(EPS_CODE以03开头)
     *
     * @param projectList 项目主键列表
     * @return 自建项目数量，大于0表示存在自建项目
     */
    Integer checkSelfBuildProjects(@Param("projectList") List<String> projectList);

    /**
     * 根据主键列表批量查询付款合同
     *
     * @param pkContrList 付款合同主键列表
     * @return 付款合同列表
     */
    List<PmContr> listPmContrByPk(@Param("pkContrList") List<String> pkContrList);

    /**
     * 根据主键列表批量查询费用结算单
     *
     * @param pkFeebalanceList 费用结算单主键列表
     * @return 费用结算单列表
     */
    List<PmFeebalanceB> listPmFeebalanceByPks(@Param("pkFeebalanceList") List<String> pkFeebalanceList);

    @Select("<script> SELECT FREPLENISHFLAG  " +
            "FROM IC_PURCHASEIN_H T1 " +
            "LEFT JOIN IA_I2BILL_B T3 ON T1.CGENERALHID=T3.CSRCID   " +
            "WHERE T3.CBILL_BID = #{cbillBid} and rownum=1 </script>")
    String getFreplenishflagTypeOne(@Param("cbillBid") String cbillBid);

    @Select("<script> SELECT FREPLENISHFLAG   " +
            "FROM IC_PURCHASEIN_H T1  " +
            "LEFT JOIN PO_SETTLEBILL_B T2 ON T1.CGENERALHID=T2.PK_STOCK AND T2.VSTOCKBILLTYPE ='45'  " +
            "LEFT JOIN IA_I2BILL_B T3 ON T2.PK_SETTLEBILL=T3.CSRCID    " +
            "WHERE T3.CBILL_BID = #{cbillBid} and rownum=1 </script>")
    String getFreplenishflagTypeTwo(@Param("cbillBid") String cbillBid);

    /**
     * 批量获取组织UCN信息
     *
     * @param pkOrgs 组织主键集合
     * @return Map<组织主键, UCN>
     */
    @MapKey("uuid")
    Map<String, UCN> batchGetOrgUcn(@Param("pkOrgs") Set<String> pkOrgs);

    @MapKey("pkBankaccsub")
    Map<String, Account> listAccountsByPks(@Param("pks") List<String> pks);

    @Select("<script> SELECT T1.*  " +
            "FROM AP_PAYBILL T1 " +
            "WHERE T1.pk_Paybill = #{pkPaybill} and T1.dr=0 </script>")
    FiSourceBasePaymentOrderVO getPaymentOrderVO(@Param("pkPaybill") String pkPaybill);

    @Select("<script> SELECT T1.*  " +
            "FROM AP_PAYITEM T1 " +
            "WHERE T1.pk_Paybill = #{pkPaybill} and T1.dr=0 </script>")
    List<FiSourceBasePaymentOrderDetailVO> getPaymentOrderDetailVO(String pkPaybill);

    @Select("<script> SELECT T1.BILL_CODE  " +
            "FROM pm_contr T1 " +
            "WHERE T1.pk_contr = #{srcBillid} and T1.dr=0 </script>")
    String getPmContr(@Param("pkPaybill") String srcBillid);

    @Select("<script> SELECT T1.BILL_CODE  " +
            "FROM pm_feebalance T1 " +
            "WHERE T1.pk_feebalance = #{srcBillid} and T1.dr=0 </script>")
    String getPmFeebalance(@Param("pkPaybill") String srcBillid);

    PayBill selectPayBillBySrcBillid(String srcBillId);

    List<Verifydetail> findVerifydetailsByPkBill2(@Param("pkBill2") String pkBill2);

    /**
     * 查询预付款是否已核销
     * @param pkContr 付款合同主键
     * @return 已核销预付款数量
     */
    int countVerifiedPrePaymentByContract(@Param("pkContr") String pkContr);
}
