
package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.admin.api.entity.SysDictItem;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiConvertReceivableDTO;
import com.cloud.ficonsumer.dto.FiConvertReceivableDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceReceivableDTO;
import com.cloud.ficonsumer.dto.FiSourceReceivableDetailDTO;
import com.cloud.ficonsumer.entity.BdUdsResult;
import com.cloud.ficonsumer.entity.FiConvertReceivable;
import com.cloud.ficonsumer.entity.FiConvertReceivableDetail;
import com.cloud.ficonsumer.entity.FiSourcePuPaybill;
import com.cloud.ficonsumer.entity.FiSourceReceivable;
import com.cloud.ficonsumer.entity.FiSourceReceivableDetail;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.ApiUpmsMapper;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceReceivableMapper;
import com.cloud.ficonsumer.mapper.FileManageMapper;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.service.FiConvertReceivableDetailService;
import com.cloud.ficonsumer.service.FiConvertReceivableService;
import com.cloud.ficonsumer.service.FiSourceFileOcrInfoService;
import com.cloud.ficonsumer.service.FiSourceReceivableDetailService;
import com.cloud.ficonsumer.service.FiSourceReceivableService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.AdvClOffVO;
import com.cloud.ficonsumer.vo.CosTypeVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceReceivableQueryVO;
import com.cloud.ficonsumer.vo.ImageDetail;
import com.cloud.ficonsumer.vo.InvoiceFileVO;
import com.cloud.ficonsumer.vo.InvoiceVO;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import com.cloud.ficonsumer.vo.YgReceivableDetailVO;
import com.cloud.ficonsumer.vo.YgReceivableVO;
import com.cloud.ficonsumer.vo.YgResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static java.util.stream.Collectors.toList;

/**
 * 项目应收单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:25
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourceReceivableServiceImpl extends ServiceImpl<FiSourceReceivableMapper, FiSourceReceivable> implements FiSourceReceivableService {

    private final FiSourceReceivableDetailService fiSourceReceivableDetailService;
    private final FiConvertReceivableService fiConvertReceivableService;
    private final FiConvertReceivableDetailService fiConvertReceivableDetailService;
    private final FiSourceFileOcrInfoService fiSourceFileOcrInfoService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final ApiUpmsMapper apiUpmsMapper;
    private final YgConfig ygConfig;
    private final FileManageMapper fileManageMapper;

    /**
     * 项目应收单重新推送
     * @param
     */
    @Override
    public boolean pushAgain(String pk) {
        FiSourceReceivable sourceMainData = this.getOne(Wrappers.<FiSourceReceivable>lambdaQuery().eq(FiSourceReceivable::getPkRecbill, pk));
        if(sourceMainData.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode())) {
            //mq的transaction = false可能出现重复消费，判断状态不进行重复推送
            return true;
        }
        List<FiSourceReceivableDetail> details = fiSourceReceivableDetailService.list(Wrappers.<FiSourceReceivableDetail>lambdaQuery().eq(FiSourceReceivableDetail::getPkRecbill, pk));
        //重新转换
        try {
            this.turnData(sourceMainData,details);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            this.updateById(sourceMainData);
            LogUtil.info(log, "应收单集成信息转换失败:", e);
            throw new CheckedException("应收单集成信息转换失败:"+pushMsg);
        }
        //重新推送智慧平台
        sourceMainData.setPushTime(DateUtil.formatDateTime(new Date()));
        try {
            YgResult ygResult = this.pushData(pk);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1l);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,Constants.TOPIC_RECEIVABLE_TRANS);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000000");
                updatePlatFormLog.setMsg("");
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TOPIC_RECEIVABLE_TRANS);
                platFormLogVO.setCode("000000");
                platFormLogVO.setMsg("");
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,Constants.TOPIC_RECEIVABLE_TRANS);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000001");
                updatePlatFormLog.setMsg(pushMsg);
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TOPIC_RECEIVABLE_TRANS);
                platFormLogVO.setCode("000001");
                platFormLogVO.setMsg(pushMsg);
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
            LogUtil.info(log, "应收单集成信息推送失败:", e);
            throw new CheckedException("应收单集成信息推送失败:"+pushMsg);
        }
        return true;
    }

    /**
     * 数据推送
     *
     * @param pk
     * @return
     * @throws Exception
     */
    private YgResult pushData(String pk) throws Exception {
        YgResult ygResult = new YgResult();
        if(ygConfig.getEnable()) {
            FiConvertReceivable convertMainData = fiConvertReceivableService.getOne(Wrappers.<FiConvertReceivable>lambdaQuery().eq(FiConvertReceivable::getPkRecbill, pk));
            List<FiConvertReceivableDetail> details = fiConvertReceivableDetailService.list(Wrappers.<FiConvertReceivableDetail>lambdaQuery().eq(FiConvertReceivableDetail::getPkRecbill,pk));
            YgReceivableVO request = this.turnYgData(convertMainData,details);
            ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000007.getServCode());
            Map<String, Object> data = new HashMap<>();
            data.put("list", Collections.singletonList(request));
            String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(data));
            ygResult = JSONObject.parseObject(result,YgResult.class);
            //成功 6100000 或 6710300 失败6990399
            if(!(ygResult.getCode().equals("6100000") || ygResult.getCode().equals("6710300"))) {
                throw new CheckedException(ygResult.getmessage());
            }
        }
        return ygResult;
    }

    /**
     * 将转换后的数据用远光提供的格式推送
     *
     * @param convertMainData
     * @param details
     * @return
     */
    private YgReceivableVO turnYgData(FiConvertReceivable convertMainData, List<FiConvertReceivableDetail> details) {
        YgReceivableVO request = new YgReceivableVO();
        String pkOrgV = apiCompareService.getTurnByType(convertMainData.getPkOrg(),Constants.PkOrg);
        request.setDwdh(pkOrgV);
        String billmaker = convertMainData.getBillmaker();
        request.setHdlgNmId(billmaker);
        request.setBiType(5);
        request.setOuterCode("ZJYJ");
        String cosType2 = "";
        String cosType3 = "";
        String cosType4 = "";
        String cosTypeName4 = "";
        CosTypeVO cosTypeVO = dmMapper.getCosTypeVOByCurr(convertMainData.getDef47());
        if(cosTypeVO == null) {
            throw new CheckedException("平台业务事项不可为空");
        }
        if(cosTypeVO.getInnercode1().length()!=16 && cosTypeVO.getInnercode1().length()!=12) {
            throw new CheckedException("平台业务事项错误");
        }else {
            if(cosTypeVO.getInnercode1() !=null && cosTypeVO.getInnercode1().length()==16) {
                cosTypeName4 = cosTypeVO.getName1();
                cosType4 = cosTypeVO.getCode1();
                cosType3 = cosTypeVO.getCode2();
                cosType2 = cosTypeVO.getCode3();
            }else if(cosTypeVO.getInnercode1() !=null && cosTypeVO.getInnercode1().length()==12) {
                cosType3 = cosTypeVO.getCode1();
                cosType2 = cosTypeVO.getCode2();
            }
        }
        request.setDisCode(convertMainData.getBillno());
        String customer = apiCompareService.getTurnByType(convertMainData.getCustomer(),Constants.PkCustomer);
        request.setCusName(customer);
        request.setIncTyp(cosType3);
//        request.setDetCosType("");
//        request.setDetCosTypeCode("");
        String contractNo = convertMainData.getContractno();
        request.setConNam(StrUtil.isNotBlank(contractNo) && contractNo.startsWith("SG")
                ? contractNo : null);
//        request.setPrjCode(convertMainData.getProject());
//        request.setPrjCode(apiCompareService.getProjectCode(convertMainData.getProject()));
//        request.setIntOrder("");
//        request.setWbsCod("");
        request.setCause(convertMainData.getScomment());
        request.setInvoType("");
        //00000000否，00000001是
        request.setInvSign("00000000");
        request.setTotalIncm(convertMainData.getLocalMoney());
        //00000000否，00000001是
        request.setDocuStatus("00000000");
//        request.setFdzs("");
        request.setBackTag(convertMainData.getBackTag());
        List<YgReceivableDetailVO> settDet = new ArrayList<>();
        List<AdvClOffVO> advClOff = new ArrayList<>();
        for(FiConvertReceivableDetail detail : details) {
            YgReceivableDetailVO ygReceivableDetailVO = new YgReceivableDetailVO();
            ygReceivableDetailVO.setIncTyp(cosType3);
//            ygReceivableDetailVO.setDetCosType(detail.getPkSubjcode());
            ygReceivableDetailVO.setDetCosTypeCode("");
            ygReceivableDetailVO.setGoodsDesc("");
            ygReceivableDetailVO.setAmt("");
            ygReceivableDetailVO.setTaxrt("");
            ygReceivableDetailVO.setTotPriTax("");
            ygReceivableDetailVO.setCurreIncom(detail.getLocalNotaxDe());
            ygReceivableDetailVO.setCurTaxPosted(detail.getLocalTaxDe());
            ygReceivableDetailVO.setRecvAmt(detail.getLocalMoneyDe());
            settDet.add(ygReceivableDetailVO);
        }
        List<BdUdsResult> bdUdsResults = dmMapper.listUdsResult(convertMainData.getPkRecbill(), convertMainData.getPkTradetype());
        List<ImageDetail> sub2 = bdUdsResults.stream().map(
                bdUdsResult -> {
                    ImageDetail image = new ImageDetail();
                    image.setImageType(YgConfig.imageType);
                    image.setFileResId(bdUdsResult.getDocumentId());
                    image.setFileName(bdUdsResult.getFileName());
                    image.setFileSource("UDS");
                    image.setFromFileSystemId(YgConfig.fromFileSystemId);
                    return image;
                }).collect(toList());
        //1. 单据不存在上传的发票，传收据
        //2.单据存在上传的发票，且存在一张同步成功的发票，同步成功的发票
        //3.单据所有上传的发票没有任意一条同步成功，不允许同步，后续定时任务继续同步
        List<InvoiceVO> generbillistatInvoice = new ArrayList<>();
        Map<String, Integer> filePushStatus = new HashMap<>();
        List<InvoiceFileVO> invoiceVOList = fileManageMapper.getInvoiceByPk(convertMainData.getPkRecbill());
        if(CollectionUtil.isNotEmpty(invoiceVOList)) {
            List<String> sourceIdList = new ArrayList<>();
            for(InvoiceFileVO invoiceFileVO : invoiceVOList) {
                sourceIdList.add(invoiceFileVO.getSourceId());
            }
            filePushStatus = fiSourceFileOcrInfoService.listOcrPushStatus(sourceIdList);
            for(InvoiceFileVO item : invoiceVOList) {
                Integer pushStatus = filePushStatus.get(item.getSourceId());
                if(pushStatus!=null && pushStatus==3) {
                    InvoiceVO invoiceVO = new InvoiceVO();
                    invoiceVO.setInvoiceNo(item.getInvoiceNo());
                    invoiceVO.setInvDate(item.getInvDate());
                    invoiceVO.setInvoCode(item.getInvoCode());
                    invoiceVO.setInvoType(item.getInvoType());
                    generbillistatInvoice.add(invoiceVO);
                }
            }
        }
        if(CollectionUtil.isEmpty(generbillistatInvoice)) {
            boolean checkInvoiceFlag = this.getCheckInvoice(convertMainData.getPkRecbill());
            if(checkInvoiceFlag) {
                throw new CheckedException("请先完成发票入池");
            }
            generbillistatInvoice = new ArrayList<>();
//            InvoiceVO invoiceVO = new InvoiceVO();
//            invoiceVO.setInvoType(YgConfig.invoType);
//            generbillistatInvoice.add(invoiceVO);
        }
        request.setSettDet(settDet);
        request.setAdvClOff(advClOff);
        request.setInvDet(generbillistatInvoice);
        request.setSub2(sub2);
        return request;
    }

    /**
     * 查看是否需要需要发票必填
     *
     * @param pkPayablebill
     * @return
     */
    private boolean getCheckInvoice(String pkPayablebill) {
        List<String> dictTypeList = new ArrayList<>();
        dictTypeList.add("999");
        List<SysDictItem> ocrTypeList = apiUpmsMapper.getItemListByType("filemanage_ocr_type");
        for(SysDictItem sysDictItem : ocrTypeList) {
            if(StrUtil.isNotBlank(sysDictItem.getRemarks()) && sysDictItem.getRemarks().equals("是")) {
                dictTypeList.add(sysDictItem.getValue());
            }
        }
        List<Long> mainIdList = fileManageMapper.getSourceInvoiceList(pkPayablebill,dictTypeList);
        if(CollectionUtil.isNotEmpty(mainIdList)) {
            return true;
        }
        return false;
    }

    /**
     * 数据转换
     *
     * @param sourceMainData
     * @param details
     */
    private void turnData(FiSourceReceivable sourceMainData, List<FiSourceReceivableDetail> details) throws Exception {
        String pk = sourceMainData.getPkRecbill();
        FiConvertReceivable fiConvertReceivable = new FiConvertReceivable();
        apiCompareCache.convertData(sourceMainData,fiConvertReceivable,BillTypeEnum.RECEIVABLE.getCode());
        FiConvertReceivable convertMainData = fiConvertReceivableService.getOne(Wrappers.<FiConvertReceivable>lambdaQuery().eq(FiConvertReceivable::getPkRecbill, pk));
        fiConvertReceivable.setId(null);
        if(convertMainData != null) {
            fiConvertReceivable.setId(convertMainData.getId());
            fiConvertReceivableDetailService.remove(Wrappers.<FiConvertReceivableDetail>lambdaQuery().eq(FiConvertReceivableDetail::getPkRecbill,pk));
        }
        List<FiConvertReceivableDetail> convertDetailList = new ArrayList<>();
        for(FiSourceReceivableDetail sourceDetailData : details) {
            FiConvertReceivableDetail fiConvertReceivableDetail = new FiConvertReceivableDetail();
            apiCompareCache.convertData(sourceDetailData,fiConvertReceivableDetail,BillTypeEnum.RECEIVABLE_DETAIL.getCode());
            fiConvertReceivableDetail.setId(null);
            convertDetailList.add(fiConvertReceivableDetail);
        }
        fiConvertReceivableService.saveOrUpdate(fiConvertReceivable);
        fiConvertReceivableDetailService.saveOrUpdateBatch(convertDetailList);
    }

    @Override
    public Page<FiSourceReceivableDTO> beforeConvertDataPage(Page page, FiSourceReceivableQueryVO queryVO) {
        Page<FiSourceReceivableDTO> result = this.baseMapper.beforeConvertDataPage(page,queryVO);
        if (CollectionUtil.isNotEmpty(result.getRecords())) {
            //转为名称
            for (FiSourceReceivableDTO data:result.getRecords()){
                if(data.getPkTradetype().equals("F0-Cxx-01")) {
                    data.setPkTradetypeName("项目应收单");
                } else if(data.getPkTradetype().equals("F0-Cxx-02")) {
                    data.setPkTradetypeName("销售应收单");
                }
            }
        }
        return result;
    }

    @Override
    public Page<FiSourceReceivableDetailDTO> getBeforeConvertDataPage(Page page, FiSourceReceivableQueryVO queryVO) {
        Page<FiSourceReceivableDetailDTO> result = this.baseMapper.getBeforeConvertDataPage(page,queryVO);
        return result;
    }

    @Override
    public List<FiConvertReceivableDTO> getConvertList(String pk) {
        List<FiConvertReceivableDTO> convertDataDTOList = new ArrayList<>();
        FiConvertReceivableDTO convertDataDTO = new FiConvertReceivableDTO();
        FiConvertReceivable convertDataOld = fiConvertReceivableService.getOne(Wrappers.<FiConvertReceivable>lambdaQuery().eq(FiConvertReceivable::getPkRecbill, pk));
        if(null != convertDataOld) {
            BeanUtil.copyProperties(convertDataOld,convertDataDTO);
            convertDataDTOList.add(convertDataDTO);
        }
        return convertDataDTOList;
    }

    @Override
    public Page<FiConvertReceivableDetailDTO> getConvertData(Page page, String pk) {
        Page<FiConvertReceivableDetailDTO> result = this.baseMapper.getConvertData(page,pk);
        return result;
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceReceivable entity = this.getById(id);
            try {
                this.pushAgain(entity.getPkRecbill());
            } catch (Exception e) {
                resultList.add(entity.getBillno()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}
