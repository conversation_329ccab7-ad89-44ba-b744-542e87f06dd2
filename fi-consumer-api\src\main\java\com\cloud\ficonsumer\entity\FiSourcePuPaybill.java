package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@TableName("fi_source_pu_paybill")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购付款源数据详情")
public class FiSourcePuPaybill extends BaseEntity<FiSourcePuPaybill> {
    private static final long serialVersionUID = -1L;

    @TableId
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("结算单主键")
    private String pkSettlement;

    @ApiModelProperty("财务组织")
    private String pkOrg;

    @ApiModelProperty(value = "应付财务组织编码", required = true)
    private String pkOrgCode;

    @ApiModelProperty(value = "应付财务组织名称", required = true)
    private String pkOrgName;

    @ApiModelProperty("业务单据编号")
    private String billcode;

    @ApiModelProperty("订单编号（采购使用）")
    private String vbillcode;

    @ApiModelProperty("合同号（项目付款使用）")
    private String cbillcode;

    @ApiModelProperty("业务单据录入人")
    private String pkBilloperator;

    @ApiModelProperty("原币金额")
    private BigDecimal primal;

    @ApiModelProperty("费用类型")
    private String expenseType;

    @ApiModelProperty("付款类型编码")
    private String pkTradetype;

    /**
     * 单据类型 当F3-Cxx-01项目付款的时候 （F3-Cxx-02采购付款时候无）
     * 4D42 付款合同
     * 4D83 费用结算单
     */
    @ApiModelProperty("付款类型编码")
    private String pkBilltype;

    @ApiModelProperty("付款主键")
    private String pkPaybill;

    @ApiModelProperty("集成状态")
    private String integrationStatus;

    @ApiModelProperty("4:推送中、1:转换失败、2:推送失败、3:推送成功、5:推送财务失败")
    private Integer pushStatus;

    @ApiModelProperty("推送成功失败信息")
    private String pushMsg;

    /**
     * 推送时间
     */
    @ApiModelProperty(value="推送时间")
    private String pushTime;

    @ApiModelProperty("返回同步消息")
    private String returnMsg;

    @ApiModelProperty("返回同步状态")
    private String returnStatus;
    /**
     * 返回同步单号
     */
    @ApiModelProperty(value="返回同步单号")
    private String returnBill;

    /**
     * 返回接收时间
     */
    @ApiModelProperty(value="返回接收时间")
    private String returnTime;

    @ApiModelProperty("重试次数")
    private Long tryNumber;

    @ApiModelProperty("0 未就绪 1 就绪")
    private Long readiness;

    @ApiModelProperty(value = "是否退回重传标识，Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N")
    private String backTag;

    @ApiModelProperty(value = "付款性质")
    private String def47;

    @ApiModelProperty(value = "部门分类")
    private String def52;
}