package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

/**
 * 应付单预付款核销检查
 */
@Data
public class PayablePreResult {

    @ApiModelProperty("付款合同主键")
    private String pkContr;

    @ApiModelProperty("付款合同单号")
    private String contractNo;

    @ApiModelProperty("费用结算主键")
    private String pkFeebalance;

    @ApiModelProperty("费用结算单号")
    private String feebalanceNo;

    @ApiModelProperty("预付款单主键")
    private String prePkPaybill;

    @ApiModelProperty("预付款单单号")
    private String prePaybillNo;

    @ApiModelProperty("核销应付单主键")
    private String pkPayable;

    @ApiModelProperty("核销应付单单号")
    private String payableNo;

    @ApiModelProperty("当前应付预付款流程类型")
    private PayablePreEnum payablePreEnum;

    @Getter
    public enum PayablePreEnum {

        NO_PRE("1", "合同不存在预付款"),

        PRE_UNVERIFIED("2", "合同存在预付款，预付款未核销"),

        VERIFIED("3", "合同存在预付款已核销，由当前应付单核销"),

        NON_CURRENT_VERIFIED("4", "存在预付款已核销，非当前应付单核销");

        private final String code;

        private final String name;

        PayablePreEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
