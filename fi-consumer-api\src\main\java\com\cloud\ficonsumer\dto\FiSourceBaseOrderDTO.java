package com.cloud.ficonsumer.dto;

import com.cloud.ficonsumer.entity.FiSourceBaseOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/06/03.
 */
@Data
public class FiSourceBaseOrderDTO extends FiSourceBaseOrder {

    @ApiModelProperty(value="供应商名称")
    private String pkSupplierName;

    @ApiModelProperty(value="付款合同主键")
    private String pkContr;

    @ApiModelProperty(value="付款合同单号")
    private String billCode;

    /**
     * 币种中文
     */
    @ApiModelProperty(value="币种中文")
    private String corigcurrencyidName;
}
