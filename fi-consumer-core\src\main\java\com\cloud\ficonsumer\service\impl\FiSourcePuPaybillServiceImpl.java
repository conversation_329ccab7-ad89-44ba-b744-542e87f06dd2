package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillQueryDTO;
import com.cloud.ficonsumer.dto.YgResp;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.enums.*;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourcePuPaybillMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

import static com.cloud.ficonsumer.enums.Constants.TradeType.*;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourcePuPaybillServiceImpl extends ServiceImpl<FiSourcePuPaybillMapper, FiSourcePuPaybill>
        implements FiSourcePuPaybillService {

    private final FiSourcePuPaybillDetailService detailService;
    private final FiConvertPuPaybillService convertService;
    private final FiConvertPuPaybillDetailService convertDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final YgConfig ygConfig;
    private final FiSourceOrderService fiSourceOrderService;
    private final FiSourcePuPayableService fiSourcePuPayableService;
    private final PlatformLogService platformLogService;
    private final FiSourceVirtualOrderService fiSourceVirtualOrderService;
    private final FiSourceBaseOrderService fiSourceBaseOrderService;
    private final FiSourceBaseOrderDetailService fiSourceBaseOrderDetailService;

    @Override
    public boolean push(String pkSettlement) {
        FiSourcePuPaybill source = this.getOne(Wrappers.<FiSourcePuPaybill>lambdaQuery()
                .eq(FiSourcePuPaybill::getPkSettlement, pkSettlement));
        List<FiSourcePuPaybillDetail> sourceDetails = detailService.list(Wrappers.<FiSourcePuPaybillDetail>lambdaQuery()
                .eq(FiSourcePuPaybillDetail::getPkSettlement, pkSettlement));
        // 1. 转换数据
        try {
            //项目付款单 需要将“预付款”生成为订单
            if(source.getPkTradetype().equals("F3-Cxx-01")) {
                //1.付款性质为预付款时
                String prepay = sourceDetails.get(0).getPrepay();
                if("1".equals(prepay)) {
                    FiSourceBaseOrder fiSourceBaseOrder = fiSourceBaseOrderService.getOne(Wrappers.<FiSourceBaseOrder>lambdaQuery()
                            .eq(FiSourceBaseOrder::getPkMain, source.getPkPaybill()));
                    if(null == fiSourceBaseOrder) {
                        this.turnPaymentOrder(source);
                    }
                }
            }
            convertData(source, sourceDetails);
        } catch (Exception e) {
            handleConversionException(source, e);
        }

        // 2. 推送数据到智慧平台
        try {
            pushToYgPlatform(source);
            updatePushSuccess(source);
        } catch (Exception e) {
            handlePushException(source, e);
        }

        return true;
    }

    private void turnPaymentOrder(FiSourcePuPaybill fiSourcePuPaybill) {
        FiSourceBasePaymentOrderVO fiSourceBasePaymentOrderVO = dmMapper.getPaymentOrderVO(fiSourcePuPaybill.getPkPaybill());
        List<FiSourceBasePaymentOrderDetailVO> detailVOList = dmMapper.getPaymentOrderDetailVO(fiSourcePuPaybill.getPkPaybill());
        FiSourceBaseOrder fiSourceBaseOrder = new FiSourceBaseOrder();
        List<FiSourceBaseOrderDetail> fiSourceBaseOrderDetailList = new ArrayList<>();
        fiSourceBaseOrder.setPkMain(fiSourceBasePaymentOrderVO.getPkPaybill());
        fiSourceBaseOrder.setSource(fiSourceBasePaymentOrderVO.getPkBilltype());
        fiSourceBaseOrder.setPkOrg(fiSourcePuPaybill.getPkOrg());
        fiSourceBaseOrder.setPkOrgName(fiSourcePuPaybill.getPkOrgName());
        String prvMkCode = "00003010";
        fiSourceBaseOrder.setPrvMkCode(prvMkCode);
        fiSourceBaseOrder.setAflUnit(fiSourcePuPaybill.getPkOrg());
        fiSourceBaseOrder.setCreTime(fiSourceBasePaymentOrderVO.getEffectdate());
        fiSourceBaseOrder.setCrePersNm(fiSourceBasePaymentOrderVO.getBillmaker());
        fiSourceBaseOrder.setSupCod(fiSourceBasePaymentOrderVO.getSupplier());
        fiSourceBaseOrder.setPurOrdTyp("0L08");
        fiSourceBaseOrder.setDataSouSys("ZJYJ");
        FiSourceBasePaymentOrderDetailVO firstDetail = detailVOList.get(0);
        String source = "";
        String sourceNo = "";
        if(firstDetail.getPkBilltype().equals("4D42")) {
            source = "4D42";
            sourceNo = dmMapper.getPmContr(firstDetail.getSrcBillid());
        } else if(firstDetail.getPkBilltype().equals("4D83")) {
            source = "4D83";
            sourceNo = dmMapper.getPmFeebalance(firstDetail.getSrcBillid());
        }
        fiSourceBaseOrder.setSource(source);
        fiSourceBaseOrder.setSourceNo(sourceNo);
        if(StrUtil.isBlank(sourceNo)) {
            throw new CheckedException("未找到单据编号");
        }
        FiSourceBaseOrder maxSourceOrder = fiSourceBaseOrderService.getOne(Wrappers.<FiSourceBaseOrder>lambdaQuery()
                .eq(FiSourceBaseOrder::getSource, source).eq(FiSourceBaseOrder::getSourceNo, sourceNo).orderByDesc(FiSourceBaseOrder::getMaxSource).last("limit 1"));
        if(null == maxSourceOrder) {
            fiSourceBaseOrder.setMaxSource(1);
        }else {
            fiSourceBaseOrder.setMaxSource(maxSourceOrder.getMaxSource()+1);
        }
        String flowNum = String.format("%03d", fiSourceBaseOrder.getMaxSource());
        fiSourceBaseOrder.setPurOrdNo(fiSourceBaseOrder.getSourceNo()+flowNum);
        BigDecimal localNotaxDe = BigDecimal.ZERO;
        for(FiSourceBasePaymentOrderDetailVO detailVO : detailVOList) {
            FiSourceBaseOrderDetail fiSourceBaseOrderDetail = new FiSourceBaseOrderDetail();
            localNotaxDe = localNotaxDe.add(new BigDecimal(detailVO.getLocalNotaxDe()));
            fiSourceBaseOrderDetail.setPkMain(fiSourceBaseOrder.getPkMain());
            fiSourceBaseOrderDetail.setPurOrdNo(fiSourceBaseOrder.getPurOrdNo());
            fiSourceBaseOrderDetail.setPurOrdItem(detailVO.getRowno());
            fiSourceBaseOrderDetail.setPrjCode(detailVO.getProject());
            fiSourceBaseOrderDetail.setItTotAmExTax(detailVO.getLocalNotaxDe());
            fiSourceBaseOrderDetail.setTaxrt(detailVO.getTaxrate());
            fiSourceBaseOrderDetail.setTaxAmount(detailVO.getLocalTaxDe());
            fiSourceBaseOrderDetail.setItTotAmIncTax(detailVO.getLocalMoneyDe());
            fiSourceBaseOrderDetail.setAmt(detailVO.getLocalMoneyDe());
            fiSourceBaseOrderDetail.setRecepId("否");
            fiSourceBaseOrderDetail.setPrvMkCode(prvMkCode);
            fiSourceBaseOrderDetailList.add(fiSourceBaseOrderDetail);
        }
        fiSourceBasePaymentOrderVO.setLocalNotaxDe(localNotaxDe.toPlainString());
        fiSourceBaseOrder.setTotAmExTax(fiSourceBasePaymentOrderVO.getLocalNotaxDe());
        fiSourceBaseOrderService.save(fiSourceBaseOrder);
        fiSourceBaseOrderDetailService.saveBatch(fiSourceBaseOrderDetailList);
        fiSourceBaseOrderService.pushAgain(fiSourceBaseOrder.getPkMain());
    }

    @Override
    public Page<FiSourcePuPaybillVO> beforeConvertDataPage(Page<FiSourcePuPaybillVO> page, FiSourcePuPaybillQueryDTO queryDTO) {
        Page<FiSourcePuPaybillVO> resulte = baseMapper.beforeConvertDataPage(page, queryDTO);
        if (CollectionUtil.isNotEmpty(resulte.getRecords())){
            for (FiSourcePuPaybillVO data : resulte.getRecords()){
                data.setReturnStatusName(ReturnStatusEnum.getByCode(data.getReturnStatus()).getName());
                if(StrUtil.isNotBlank(data.getPkTradetype())) {
                    if(data.getPkTradetype().equals("F3-Cxx-01")) {
                        data.setPkTradetypeName("项目付款单");
                    }else if(data.getPkTradetype().equals("F3-Cxx-02") ||data.getPkTradetype().equals("F3-Cxx-09")) {
                        data.setPkTradetypeName("采购付款单");
                    }
                }
                FiSourcePuPaybillDetail detail = detailService.getOne(Wrappers.<FiSourcePuPaybillDetail>lambdaQuery()
                        .eq(FiSourcePuPaybillDetail::getPkSettlement, data.getPkSettlement()).last("limit 1"));
                if(null != detail) {
                    data.setPkSupplierName(dmMapper.getSupplierName(detail.getSupplier()));
                    ApiCompare apiCompare = apiCompareCache.getDictionaryValue(BillTypeEnum.PU_PAYBILL_DETAIL.getCode(), "pk_currtype_last", YgUtil.toStringTrim(detail.getPkCurrtypeLast()));
                    data.setPkCurrtypeLastName(Objects.isNull(apiCompare)?null:apiCompare.getTargetSystemCode());
                }
                ApiCompare apiCompare = apiCompareCache.getDictionaryValue(BillTypeEnum.PU_PAYBILL.getCode(), "pk_billoperator", YgUtil.toStringTrim(data.getPkBilloperator()));
                data.setPkBilloperatorName(Objects.isNull(apiCompare)?null:apiCompare.getTargetSystemName());
            }
        }
        return resulte;
    }

    @Override
    public Page<FiSourcePuPaybillDetailVO> getBeforeConvertDataPage(Page<FiSourcePuPaybillDetailVO> page, FiSourcePuPaybillDetailQueryDTO queryDTO) {
        return baseMapper.getBeforeConvertDataPage(page, queryDTO);
    }

    @Override
    public List<FiConvertPuPaybillVO> getConvertList(String pkSettlement) {
        return baseMapper.getConvertList(pkSettlement);
    }

    @Override
    public Page<FiConvertPuPaybillDetailVO> getConvertData(Page<FiConvertPuPaybillDetailVO> page, String pkSettlement) {
        return baseMapper.getConvertData(page, pkSettlement);
    }

    @Override
    public Map<String, FiSourcePuPaybillDetailVO> getNoAccountedBySrcBillIds(FiSourcePuPaybillQueryDTO queryDTO) {
        if (Objects.isNull(queryDTO)) {
            return Collections.emptyMap();
        }
        return baseMapper.getNoAccountedPrepayBySrcBillIds(queryDTO);
    }

    /**
     * 更新主单推送成功的状态，并保存成功日志
     */
    private void updatePushSuccess(FiSourcePuPaybill source) {
        source.setIntegrationStatus("0");
        source.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
        source.setPushMsg("");
        source.setReadiness(1L);
        source.setReturnStatus("");
        source.setReturnMsg("");
        source.setReturnTime("");
        source.setReturnBill("");
        this.updateById(source);

        // 保存成功日志
        platformLogService.recordLog(
            source.getPkSettlement(),
            source.getPkTradetype(),
            Constants.SUCCESS,
            "【采购-预付付款申请单接收服务 (结算对照)】，集成财务中台成功"
        );
    }

    /**
     * 记录转换异常，更新状态，并抛出 CheckedException
     */
    private void handleConversionException(FiSourcePuPaybill source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        // 保存转换失败日志
        platformLogService.recordLog(
            source.getPkSettlement(),
            source.getPkTradetype(),
            Constants.FAIL,
            pushMsg
        );

        LogUtil.info(log, "采购结算单集成信息转换失败:", e);
        throw new CheckedException("采购结算单集成信息转换失败:" + pushMsg);
    }

    /**
     * 记录推送异常，更新状态，并抛出 CheckedException
     */
    private void handlePushException(FiSourcePuPaybill source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        // 保存推送失败日志
        platformLogService.recordLog(
            source.getPkSettlement(),
            source.getPkTradetype(),
            Constants.FAIL,
            pushMsg
        );

        LogUtil.info(log, "采购结算单集成信息推送失败:", e);
        throw new CheckedException("采购结算单集成信息推送失败:" + pushMsg);
    }

    /**
     * 推送数据至智慧平台
     */
    private Boolean pushToYgPlatform(FiSourcePuPaybill source) throws Exception {
        if (!ygConfig.getEnable()) {
            return Boolean.FALSE;
        }

        FiConvertPuPaybill convert = convertService.getOne(
                Wrappers.<FiConvertPuPaybill>lambdaQuery().eq(FiConvertPuPaybill::getPkSettlement, source.getPkSettlement()));
        List<FiConvertPuPaybillDetail> details = convertDetailService.list(
                Wrappers.<FiConvertPuPaybillDetail>lambdaQuery().eq(FiConvertPuPaybillDetail::getPkSettlement, source.getPkSettlement()));

        YgPaymentReqVO ygPaymentReqVO = createYgPaymentReqVO(convert, details);
        ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000023.getServCode());

        Map<String, Object> data = new HashMap<>();
        data.put("list", Collections.singletonList(ygPaymentReqVO));
        String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(data));
        // 对 result 的返回值进行判空处理
        if (StringUtils.isEmpty(result)) {
            throw new CheckedException("响应失败，响应为空。");
        }

        YgResult<List<YgResp>> ygResult = JSONObject.parseObject(result, 
                new TypeReference<YgResult<List<YgResp>>>() {});
        // 判断响应是否成功
        if (("6100000".equals(ygResult.getCode()) || "6710300".equals(ygResult.getCode()))) {
            throw new CheckedException(ygResult.getmessage());
        } else {
            List<YgResp> respList = ygResult.getData();
            if (CollectionUtils.isEmpty(respList)) {
                throw new CheckedException(result);
            }
            for (YgResp resp : respList) {
                if (!"0".equals(resp.getCode())) {
                    throw new CheckedException(resp.getMessage());
                }
            }
        }
        return true;
    }

    private void convertData(FiSourcePuPaybill sourceMainData, List<FiSourcePuPaybillDetail> details) throws Exception {
        String pkSettlement = sourceMainData.getPkSettlement();

        // 转换主信息
        FiConvertPuPaybill fiConvert = new FiConvertPuPaybill();
        apiCompareCache.convertData(sourceMainData, fiConvert, BillTypeEnum.PU_PAYBILL.getCode());

        // 如若已存在转换数据，则使用已有主键并清空老明细数据
        FiConvertPuPaybill existing = convertService.getOne(
                Wrappers.<FiConvertPuPaybill>lambdaQuery().eq(FiConvertPuPaybill::getPkSettlement, pkSettlement));
        if (existing != null) {
            fiConvert.setId(existing.getId());
            convertDetailService.remove(
                    Wrappers.<FiConvertPuPaybillDetail>lambdaQuery().eq(FiConvertPuPaybillDetail::getPkSettlement, pkSettlement));
        }

        // 转换明细数据
        List<FiConvertPuPaybillDetail> convertDetailList = new ArrayList<>();
        for (FiSourcePuPaybillDetail sourceDetail : details) {
            FiConvertPuPaybillDetail convertDetail = new FiConvertPuPaybillDetail();
            apiCompareCache.convertData(sourceDetail, convertDetail, BillTypeEnum.PU_PAYBILL_DETAIL.getCode());
            convertDetail.setId(null);
            convertDetailList.add(convertDetail);
        }

        convertService.saveOrUpdate(fiConvert);
        convertDetailService.saveOrUpdateBatch(convertDetailList);
    }

    private YgPaymentReqVO createYgPaymentReqVO(FiConvertPuPaybill convert, List<FiConvertPuPaybillDetail> details) {
        YgPaymentReqVO requestVO = new YgPaymentReqVO();
        requestVO.setBizappid(String.valueOf(19774));
        String pkOrg = apiCompareService.getTurnByType(convert.getPkOrg(),Constants.PkOrg);
        requestVO.setDwdh(pkOrg);
        //经办人转换MDM人员编码
        requestVO.setHdlgNmId(convert.getPkBilloperator());
        requestVO.setBiType(Integer.parseInt("0".equals(details.get(0).getPrepay())?"2":details.get(0).getPrepay()));
        requestVO.setOuterCode("ZJYJ");
        requestVO.setDisCode(convert.getBillcode());
        String projectCode = "";
        if ("F3-Cxx-01".equals(convert.getPkTradetype())){
            // 源头单据主键，项目付款单源头单据为付款合同
            List<String> srcBillIds = details.stream()
                    .map(FiConvertPuPaybillDetail::getSrcBillid)
                    .distinct()
                    .collect(toList());
            // 获取付款合同
            List<PmContr> pmContrs = dmMapper.listPmContrByPk(srcBillIds);
            if (CollectionUtils.isEmpty(pmContrs)) {
                throw new CheckedException("未找到付款合同信息:" + srcBillIds);
            }

            projectCode = apiCompareService.getProjectCode(details.get(0).getProject());
            String payType = "";
            if("1".equals(details.get(0).getPrepay())){
                payType = "00000001";
            }else {
                payType = "00000002";
            }
            requestVO.setPayType(payType);
            requestVO.setPurOrdNo(pmContrs.get(0).getBillCode());
            requestVO.setSourBusId(convert.getPkSettlement());
            // 1.付款性质为预付款时，付款合同的同步成功
            String prepay = details.get(0).getPrepay();
            if("1".equals(prepay)) {
                srcBillIds.forEach(fiSourceVirtualOrderService::checkSync);
            }
            // 2.付款性质为应付款时，源头付款合同，下游所有的应付单，平台回写状态需为已挂账
            Map<String, FiSourcePuPayableDetailVO> noPostedPayable = fiSourcePuPayableService.getNoPostedBySrcBillIds(srcBillIds);
            if (MapUtils.isNotEmpty(noPostedPayable)) {
                throw new CheckedException("存在已传平台且返回状态不为2已挂账的项目应付单:" + srcBillIds);
            }
            // 3.付款性质为应付款时，源头付款合同，下游所有的付款单，平台回写状态需为已记账
            FiSourcePuPaybillQueryDTO queryDTO = new FiSourcePuPaybillQueryDTO();
            queryDTO.setSrcBillIds(srcBillIds);
            Map<String, FiSourcePuPaybillDetailVO> noAccountedPaybills = this.getNoAccountedBySrcBillIds(queryDTO);
            noAccountedPaybills.values().removeIf(detail -> detail.getPkSettlement().equals(convert.getPkSettlement()));
            if (MapUtils.isNotEmpty(noAccountedPaybills)) {
                throw new CheckedException("存在已传平台且返回状态不为4已记账的项目付款单:" + srcBillIds);
            }
            // 费用类型相关字段
            String cosType2 = "";
            String cosType3 = "";
            String cosType4 = "";
            String cosTypeName4 = "";
            CosTypeVO cosTypeVO = dmMapper.getCosTypeVOByCurr(convert.getDef47());
            if(cosTypeVO == null) {
                throw new CheckedException("平台业务事项不可为空");
            }
            if(cosTypeVO.getInnercode1().length()!=16 && cosTypeVO.getInnercode1().length()!=12) {
                throw new CheckedException("平台业务事项错误");
            }else {
                if(cosTypeVO.getInnercode1() !=null && cosTypeVO.getInnercode1().length()==16) {
                    cosTypeName4 = cosTypeVO.getName1();
                    cosType4 = cosTypeVO.getCode1();
                    cosType3 = cosTypeVO.getCode2();
                    cosType2 = cosTypeVO.getCode3();
                }else if(cosTypeVO.getInnercode1() !=null && cosTypeVO.getInnercode1().length()==12) {
                    cosType3 = cosTypeVO.getCode1();
                    cosType2 = cosTypeVO.getCode2();
                }
            }
            requestVO.setCosType(cosType3);
            requestVO.setDetCosType(cosTypeName4);
            requestVO.setDetCosTypeCode(cosType4);
            // 部门分类
            requestVO.setMainDeptType(convert.getDef52());
        }else if ("F3-Cxx-02".equals(convert.getPkTradetype()) || "F3-Cxx-09".equals(convert.getPkTradetype())){
            String orderType = apiCompareService.getTurnByType(convert.getVbillcode(),Constants.Vbillcode);
            String payType = "";
            if("1".equals(details.get(0).getPrepay())){
                // 预付款时，传00000001预付款
                payType = "00000001";
            }else {
                // 应付款时，先判断质保金金额def38字段是否为0
                if (StringUtils.isNotEmpty(details.get(0).getDef38()) &&
                        BigDecimal.valueOf(Long.parseLong(details.get(0).getDef38())).compareTo(BigDecimal.ZERO) == 0) {
                    if (order_type_1.contains(orderType)){
                        // 以下采购订单类型传“1”：工程项目物资采购；普通采购；直运采购（销售）；直运采购（入库推出库）；国网电商采购（货物类）；销售协同采购；00000008
                        payType = "00000008";
                    } else if(order_type_2.contains(orderType)){
                        // 以下采购订单类型传“2”：服务类采购；固定资产采购（入库不传存货）；国网电商采购（服务类）00000002
                        payType = "00000002";
                    }
                } else {
                    // 质保金金额不为0时：付款性质传：质保金00000003
                    payType = "00000003";
                }
            }
            requestVO.setPayType(payType);
            requestVO.setPurOrdNo(convert.getVbillcode());
            requestVO.setSourBusId(convert.getBillcode());
            //前置条件：
            //1.付款性质为预付款时，采购订单的返回参数为成功
            String prepay = details.get(0).getPrepay();
            if("1".equals(prepay)) {
                boolean readinessFlag = fiSourceOrderService.getOrderReadiness(convert.getVbillcode());
                if(!readinessFlag){
                    throw new CheckedException("请先推送订单:" + convert.getVbillcode());
                }
            }
            //2.付款性质为预付款时，采购应付回传接口已返回回传状态为2已挂账，且采购应付更新历史订单接口已返回成功
            List<String> srcBillIds = details.stream().map(FiConvertPuPaybillDetail::getSrcBillid).collect(toList());
            if("0".equals(prepay)) {
                Map<String, FiSourcePuPayableDetailVO> noPostedPayables = fiSourcePuPayableService.getNoPostedBySrcBillIds(srcBillIds);
                if (MapUtils.isEmpty(noPostedPayables)) {
                    throw new CheckedException("存在不为已挂账的应付单:" + srcBillIds);
                }
            }
            //3.根据采购订单查询下游采购付款单中不存在“已传平台且返回状态不为4已记账”的其他采购付款单
            FiSourcePuPaybillQueryDTO queryDTO = new FiSourcePuPaybillQueryDTO();
            queryDTO.setSrcBillIds(srcBillIds);
            Map<String, FiSourcePuPaybillDetailVO> noAccountedPaybills = this.getNoAccountedBySrcBillIds(queryDTO);
            noAccountedPaybills.values().removeIf(detail -> detail.getPkSettlement().equals(convert.getPkSettlement()));
            if (MapUtils.isNotEmpty(noAccountedPaybills)) {
                throw new CheckedException("存在已传平台且返回状态不为4已记账的采购付款单:" + srcBillIds);
            }
        }
        requestVO.setCause(details.get(0).getMemo());
        requestVO.setEnginProje(projectCode);
        if(StringUtils.isNotEmpty(details.get(0).getContractNo())){
            String conNam = details.get(0).getContractNo().startsWith("SG")?details.get(0).getContractNo():"";
            requestVO.setConNam(conNam);
        }else {
            requestVO.setConNam("");
        }
        //预约付款时间 传输日期+30天
        requestVO.setOrderPayDate(DateFormatUtils.format(DateUtils.addMonths(new Date(), 1),"yyyyMMdd"));
        requestVO.setTotAmoPaid(convert.getPrimal());

        requestVO.setFdzs(0);
        requestVO.setBackTag(convert.getBackTag());
        requestVO.setModifyBillFlag("********");
        requestVO.setTarnCurren(details.get(0).getPkCurrtypeLast());
        requestVO.setExchRat(details.get(0).getChangerate());
        // 构造付款信息（行项目）列表
        List<YgPaymentReqVO.Payable> payableList = new ArrayList<>();
        for (FiConvertPuPaybillDetail detail : details) {
            YgPaymentReqVO.Payable payable = new YgPaymentReqVO.Payable();
            String pkSupplier = apiCompareService.getTurnByType(detail.getSupplier(),Constants.PkSupplier);
            payable.setSupp(pkSupplier);
            payable.setMigrantWorkerWageAccount("********");
            payable.setRAcc(detail.getOppaccount());
            payable.setRcvrAccName(detail.getOppaccname());
            String bank = apiCompareService.getTurnByType(detail.getPkOppbank(),Constants.PkOppbank);
            payable.setBank(bank);
            payable.setAmtThisPay(detail.getPay());
            payable.setPrjName(projectCode);
            payableList.add(payable);
        }
        requestVO.setPayabl(payableList);
        List<ImageDetail> cred = new ArrayList<>();
        requestVO.setCred(cred);
        return requestVO;
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourcePuPaybill entity = this.getById(id);
            try {
                this.push(entity.getPkSettlement());
            } catch (Exception e) {
                resultList.add(entity.getBillcode()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}
