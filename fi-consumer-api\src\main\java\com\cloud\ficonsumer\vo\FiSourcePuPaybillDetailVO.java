package com.cloud.ficonsumer.vo;

import com.cloud.ficonsumer.entity.FiSourcePuPaybillDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购付款明细源数据详情VO")
public class FiSourcePuPaybillDetailVO extends FiSourcePuPaybillDetail {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "单据号")
    private String billno;

    /**
     * 单据类型 当F3-Cxx-01项目付款的时候 （F3-Cxx-02采购付款时候无）
     * 4D42 付款合同
     * 4D83 费用结算单
     */
    @ApiModelProperty("付款类型编码")
    private String pkBilltype;

    /**
     * 当单据类型是F3-Cxx-01得时候根据srcBillid查询付款合同和费用结算
     */
    @ApiModelProperty("来源主键")
    private String srcBillid;

    /**
     * 当单据类型是F3-Cxx-02和F3-Cxx-09（采购相关的，详情里的PURCHASEORDER就是采购订单号）
     */
    @ApiModelProperty("采购单号")
    private String PURCHASEORDER;
}
