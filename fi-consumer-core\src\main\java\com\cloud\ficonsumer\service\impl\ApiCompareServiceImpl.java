
package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.apiexchange.entity.ApiInfoHis;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.mapper.ApiCompareMapper;
import com.cloud.ficonsumer.mapper.ApiExchangeMapper;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.vo.ApiCompareVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 对照表
 *
 * <AUTHOR> code generator
 * @date 2024-07-12 10:14:10
 */
@Service
@AllArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class ApiCompareServiceImpl extends ServiceImpl<ApiCompareMapper, ApiCompare> implements ApiCompareService {

    private final ApiCompareCache apiCompareCache;
    private final DmMapper dmMapper;
    private final ApiExchangeMapper apiExchangeMapper;

    @Override
    public Boolean saveApiCompare(ApiCompareVO apiCompare) {
        List<ApiCompare> compareList = list(Wrappers.<ApiCompare>lambdaQuery().eq(ApiCompare::getFileType, apiCompare.getFileType())
                .eq(ApiCompare::getSourceSystemId, apiCompare.getSourceSystemId())
                .eq(ApiCompare::getSourceSystemCode, apiCompare.getSourceSystemCode())
                .eq(ApiCompare::getSourceSystemName, apiCompare.getSourceSystemName())
                .eq(ApiCompare::getTargetSystemId, apiCompare.getTargetSystemId())
                .eq(ApiCompare::getTargetSystemCode, apiCompare.getTargetSystemCode())
                .eq(ApiCompare::getTargetSystemName, apiCompare.getTargetSystemName()));
        if (CollectionUtil.isNotEmpty(compareList)){
            throw new CheckedException("请勿添加重复数据");
        }
        this.save(apiCompare);
        //刷新缓存
        apiCompareCache.reloadCompareCache();
        return true;
    }

    @Override
    public Boolean editApiCompare(ApiCompareVO apiCompare) {
        List<ApiCompare> compareList = list(Wrappers.<ApiCompare>lambdaQuery().eq(ApiCompare::getFileType, apiCompare.getFileType())
                .eq(ApiCompare::getSourceSystemId, apiCompare.getSourceSystemId())
                .eq(ApiCompare::getSourceSystemCode, apiCompare.getSourceSystemCode())
                .eq(ApiCompare::getSourceSystemName, apiCompare.getSourceSystemName())
                .eq(ApiCompare::getTargetSystemId, apiCompare.getTargetSystemId())
                .eq(ApiCompare::getTargetSystemCode, apiCompare.getTargetSystemCode())
                .eq(ApiCompare::getTargetSystemName, apiCompare.getTargetSystemName())
                .ne(ApiCompare::getId,apiCompare.getId()));
        if (CollectionUtil.isNotEmpty(compareList)){
            throw new CheckedException("数据库中已经有该数据，请重新修改");
        }
        this.updateById(apiCompare);
        //刷新缓存
        apiCompareCache.reloadCompareCache();
        return true;
    }

    @Override
    public Boolean delById(Long id) {
        this.removeById(id);
        //刷新缓存
        apiCompareCache.reloadCompareCache();
        return true;
    }

    @Override
    public boolean delBatchById(List<Long> ids) {
        this.removeByIds(ids);
        //刷新缓存
        apiCompareCache.reloadCompareCache();
        return true;
    }

    @Override
    public Page<ApiCompare> getApiComparePage(Page page, ApiCompareVO apiCompare) {
        return this.page(page,Wrappers.<ApiCompare>lambdaQuery()
                                        .eq(StringUtils.isNotBlank(apiCompare.getFileType()),ApiCompare::getFileType,apiCompare.getFileType())
                                        .like(StringUtils.isNotBlank(apiCompare.getSourceSystemCode()),ApiCompare::getSourceSystemCode,apiCompare.getSourceSystemCode())
                                        .like(StringUtils.isNotBlank(apiCompare.getSourceSystemId()),ApiCompare::getSourceSystemId,apiCompare.getSourceSystemId())
                                        .like(StringUtils.isNotBlank(apiCompare.getTargetSystemId()),ApiCompare::getTargetSystemId,apiCompare.getTargetSystemId())
                                        .like(StringUtils.isNotBlank(apiCompare.getTargetSystemCode()),ApiCompare::getTargetSystemCode,apiCompare.getTargetSystemCode())
                                        .like(StringUtils.isNotBlank(apiCompare.getTargetSystemName()),ApiCompare::getTargetSystemName,apiCompare.getTargetSystemName())
                                        .like(StringUtils.isNotBlank(apiCompare.getSourceSystemName()),ApiCompare::getSourceSystemName,apiCompare.getSourceSystemName()));
    }

    @Override
    public ApiExchange getApiExchangeByCode(String code) {
        return apiExchangeMapper.getApiExchangeByCode(code);
    }

    @Override
    public int saveApiInfoHis(ApiInfoHis apiMsg) {
        return apiExchangeMapper.saveApiInfoHis(apiMsg);
    }

    /**
     * 根据类型和值转换
     *
     * @param value
     * @param type
     * @return
     */
    @Override
    public String getTurnByType(String value, String type) {
        String result = "";
        if(type.equals(Constants.PkOrg)) {
            //MDM组织编码10692934浙江华云信息科技有限公司
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getOrgByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("组织MDM编码查询为空");
                }
            }
        }else if(type.equals(Constants.cosType)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getCosTypeByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("平台业务事项查询为空");
                }
            }
        } else if(type.equals(Constants.PkSupplier)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getSupplierByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("供应商MDM编码查询为空");
                }
            }
        } else if(type.equals(Constants.ctrantypeid)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getCtrantypeIdByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("订单类型查询为空");
                }
            }
        }else if(type.equals(Constants.PkCustomer)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getCustomerByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("客户MDM编码查询为空");
                }
            }
        }else if(type.equals(Constants.accNum)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getAccNumByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("银行账户查询为空");
                }
            }
        } else if(type.equals(Constants.payAmo)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getPayAmoByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("付款方式查询为空");
                }
            }
        } else if(type.equals(Constants.deptType)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getDeptTypeByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("部门分类查询为空");
                }
            }
        } else if(type.equals(Constants.isAcr)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getIsAcrByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("是否计提查询为空");
                }
            }
        } else if(type.equals(Constants.project)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getProjectByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("项目编码查询为空");
                }
            }
        } else if(type.equals(Constants.orgCostregion)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getOrgCostregionByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("成本域关联组织查询为空");
                }
            }
        } else if(type.equals(Constants.PkStockOrg)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getPkStockOrgByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("仓库组织查询为空");
                }
            }
        } else if(type.equals(Constants.PkStock)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getPkStockByPk(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("仓库查询为空");
                }
            }
        } else if(type.equals(Constants.PkOppbank)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getBankCode(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("银行查询为空");
                }
            }
        } else if(type.equals(Constants.Vbillcode)) {
            if(AdminUtils.isNotBlank(value)) {
                String fieldValue = dmMapper.getBillType(value);
                if(AdminUtils.isNotBlank(fieldValue)) {
                    result = fieldValue;
                } else {
                    throw new CheckedException("订单类型查询为空");
                }
            }
        }
        return result;
    }

    @Override
    public String getProjectCode(String pkProject) {
        if(YgConfig.environment.equals("dev")) {
           return "";
        }
        if(StrUtil.isBlank(pkProject)) {
            return null;
        }
        String projectCode = this.getTurnByType(pkProject, Constants.project);
        String pkLog = dmMapper.getProjectReadiness(pkProject);
        if(StrUtil.isBlank(pkLog)) {
            throw new CheckedException("请先推送项目主数据:"+projectCode);
        }
        return projectCode;
    }
}
