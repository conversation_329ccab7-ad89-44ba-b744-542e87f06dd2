

package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiConvertBaseOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceBaseOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceBaseOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceBaseOrder;
import com.cloud.ficonsumer.vo.FiSourceBaseOrderQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:09
 */
@Mapper
public interface FiSourceBaseOrderMapper extends CloudBaseMapper<FiSourceBaseOrder> {

    /**
     * 订单转换前分页查询
     *
     * @param page
     * @param queryVO
     * @return
     */
    @Select("<script>SELECT * " +
            " FROM fi_source_base_order t " +
            " WHERE t.del_flag = 0" +
            "<if test=\"param.pkMain !=null and param.pkMain !=''\"> " +
            " and pk_main = #{param.pkMain} " +
            "</if> " +
            "<if test=\"param.purOrdNo !=null and param.purOrdNo !=''\"> " +
            " and pur_ord_no like CONCAT('%',#{param.purOrdNo},'%') " +
            "</if> " +
            "<if test=\"param.pkOrgList != null and param.pkOrgList.size() > 0\"> " +
            "   AND pk_org in " +
            "   <foreach collection=\"param.pkOrgList\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "      #{item} " +
            "   </foreach>" +
            " </if> " +
            "<if test=\"param.groupCodeFlag !=null and param.groupCodeFlag !=''\"> " +
            " and pk_org in (select pk_org " +
            "       from fi_org_compare " +
            "       where group_code = #{param.groupCodeFlag} and del_flag =0) " +
            "</if> " +
            "<if test=\"param.pushStatus !=null and param.pushStatus !=''\"> " +
            " and push_status = #{param.pushStatus} " +
            "</if> " +
            "<if test=\"param.pushStatusList != null and param.pushStatusList.size() > 0\">"+
            " and push_status in " +
            "<foreach collection=\"param.pushStatusList\" item=\"item\" open=\"(\" separator=\",\" close=\")\">"+
            "#{item}" +
            "</foreach>" +
            "</if> " +
            "<if test=\"param.createTimeStart !=null and param.createTimeStart !=''\"> " +
            " and create_time >= #{param.createTimeStart} " +
            "</if> " +
            "<if test=\"param.createTimeEnd !=null and param.createTimeEnd !=''\"> " +
            " and #{param.createTimeEnd} >= create_time " +
            "</if> " +
            " order by create_time desc  " +
            "</script>")
    Page<FiSourceBaseOrderDTO> beforeConvertDataPage(Page page, @Param("param") FiSourceBaseOrderQueryVO queryVO);

    /**
     * 转换前数据详情查询
     *
     * @param page
     * @param queryVO
     * @return
     */
    @Select("<script>SELECT td.* " +
            "    FROM fi_source_base_order t " +
            "    LEFT JOIN fi_source_base_order_detail td ON t.pk_main = td.pk_main " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_main = #{param.pkMain}" +
            "</script>")
    Page<FiSourceBaseOrderDetailDTO> getBeforeConvertDataPage(Page page, @Param("param") FiSourceBaseOrderQueryVO queryVO);

    /**
     * 转换前数据详情查询
     *
     * @param page
     * @param pk
     * @return
     */
    @Select("<script>SELECT td.* " +
            "    FROM fi_convert_base_order t " +
            "    LEFT JOIN fi_convert_base_order_detail td ON t.pk_main = td.pk_main " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_main = #{pk}" +
            "</script>")
    Page<FiConvertBaseOrderDetailDTO> getConvertData(Page page, @Param("pk") String pk);
}
