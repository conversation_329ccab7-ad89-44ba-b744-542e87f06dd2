package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "核销明细")
public class Verifydetail {

    @ApiModelProperty(value = "核销明细ID")
    private String pkVerifydetail;

    @ApiModelProperty(value = "主表ID")
    private String pkBill;

    @ApiModelProperty(value = "单据明细ID")
    private String pkItem;

    @ApiModelProperty(value = "单据类型")
    private String pkBilltype;

    @ApiModelProperty(value = "单据编号")
    private String billNo;

    @ApiModelProperty(value = "交易类型")
    private String pkTradetype;

    @ApiModelProperty(value = "单据大类")
    private String billClass;

    @ApiModelProperty(value = "对应主表ID")
    private String pkBill2;

    @ApiModelProperty(value = "对应单据明细ID")
    private String pkItem2;

    @ApiModelProperty(value = "对应单据类型")
    private String pkBilltype2;

    @ApiModelProperty(value = "对应单据编号")
    private String billNo2;

    @ApiModelProperty(value = "对应交易类型")
    private String pkTradetype2;

    @ApiModelProperty(value = "对应单据大类")
    private String billClass2;
}