
package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiConvertBaseOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertBaseOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceBaseOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceBaseOrderDetailDTO;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.entity.FiConvertBaseOrder;
import com.cloud.ficonsumer.entity.FiConvertBaseOrderDetail;
import com.cloud.ficonsumer.entity.FiSourceBaseOrder;
import com.cloud.ficonsumer.entity.FiSourceBaseOrderDetail;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.integration.dto.Param;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceBaseOrderMapper;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.service.FiConvertBaseOrderDetailService;
import com.cloud.ficonsumer.service.FiConvertBaseOrderService;
import com.cloud.ficonsumer.service.FiSourceBaseOrderDetailService;
import com.cloud.ficonsumer.service.FiSourceBaseOrderService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceBaseOrderQueryVO;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import com.cloud.ficonsumer.vo.YgOrderDetailVO;
import com.cloud.ficonsumer.vo.YgOrderVO;
import com.cloud.ficonsumer.vo.YgResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:09
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourceBaseOrderServiceImpl extends ServiceImpl<FiSourceBaseOrderMapper, FiSourceBaseOrder> implements FiSourceBaseOrderService {

    private final FiSourceBaseOrderDetailService fiSourceBaseOrderDetailService;
    private final FiConvertBaseOrderService fiConvertBaseOrderService;
    private final FiConvertBaseOrderDetailService fiConvertBaseOrderDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final YgConfig ygConfig;

    /**
     * 报错重推
     *
     * @param pk
     * @return
     */
    @Override
    public boolean pushAgain(String pk) {
        FiSourceBaseOrder sourceMainData = this.getOne(Wrappers.<FiSourceBaseOrder>lambdaQuery().eq(FiSourceBaseOrder::getPkMain, pk));
        List<FiSourceBaseOrderDetail> details = fiSourceBaseOrderDetailService.list(Wrappers.<FiSourceBaseOrderDetail>lambdaQuery().eq(FiSourceBaseOrderDetail::getPkMain, pk));
        //重新转换
        try {
            this.turnData(sourceMainData,details);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            this.updateById(sourceMainData);
            LogUtil.info(log, "订单集成信息转换失败:", e);
            throw new CheckedException("订单集成信息转换失败:"+pushMsg);
        }
        //重新推送智慧平台
        sourceMainData.setPushTime(DateUtil.formatDateTime(new Date()));
        try {
            YgResult ygResult = this.pushData(sourceMainData.getPkMain());
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1l);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,BillTypeEnum.BASE_ORDER.getCode());
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000000");
                updatePlatFormLog.setMsg("");
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(BillTypeEnum.BASE_ORDER.getCode());
                platFormLogVO.setCode("000000");
                platFormLogVO.setMsg("");
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,BillTypeEnum.BASE_ORDER.getCode());
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000001");
                updatePlatFormLog.setMsg(pushMsg);
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(BillTypeEnum.BASE_ORDER.getCode());
                platFormLogVO.setCode("000001");
                platFormLogVO.setMsg(pushMsg);
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
            LogUtil.info(log, "订单集成信息推送失败:", e);
            throw new CheckedException("订单集成信息推送失败:"+pushMsg);
        }
        return true;
    }

    /**
     * 数据推送
     *
     * @param pk
     * @return
     * @throws Exception
     */
    private YgResult pushData(String pk) throws Exception {
        YgResult ygResult = new YgResult();
        if(ygConfig.getEnable()) {
            FiConvertBaseOrder convertMainData = fiConvertBaseOrderService.getOne(Wrappers.<FiConvertBaseOrder>lambdaQuery().eq(FiConvertBaseOrder::getPkMain, pk));
            List<FiConvertBaseOrderDetail> details = fiConvertBaseOrderDetailService.list(Wrappers.<FiConvertBaseOrderDetail>lambdaQuery().eq(FiConvertBaseOrderDetail::getPkMain,pk));
            YgOrderVO ygOrderVO = this.turnYgData(convertMainData,details);
            ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000011.getServCode());
            Param<YgOrderVO> param = Param.addData(apiExchange.getCode(), apiExchange.getAccessKey(), ygOrderVO);
            String result = YgUtil.sendToYg(apiExchange, JSON.toJSONString(ygOrderVO));
            ygResult = JSONObject.parseObject(result,YgResult.class);
            if(!ygResult.getCode().equals("000000")) {
                throw new CheckedException(ygResult.getmessage());
            }
        }
        return ygResult;
    }

    /**
     * 将转换后的数据用远光提供的格式推送
     *
     * @param convertMainData
     * @param details
     * @return
     */
    private YgOrderVO turnYgData(FiConvertBaseOrder convertMainData, List<FiConvertBaseOrderDetail> details) {
        YgOrderVO ygOrderVO = new YgOrderVO();
        String pkOrgV = apiCompareService.getTurnByType(convertMainData.getAflUnit(), Constants.PkOrg);
        if(StrUtil.isBlank(pkOrgV)) {
            throw new CheckedException("单位代号不可为空");
        }
        List<YgOrderDetailVO> items = new ArrayList<>();
        ygOrderVO.setPrvMkCode(convertMainData.getPrvMkCode());
        ygOrderVO.setPurOrdNo(convertMainData.getPurOrdNo());
        ygOrderVO.setAflUnit(pkOrgV);
        ygOrderVO.setCreTime(convertMainData.getCreTime());
        ygOrderVO.setPurOrdTyp(convertMainData.getPurOrdTyp());
        String pkSupplier = apiCompareService.getTurnByType(convertMainData.getSupCod(), Constants.PkSupplier);
        ygOrderVO.setSupCod(pkSupplier);
        ygOrderVO.setTotAmExTax(convertMainData.getTotAmExTax());
        ygOrderVO.setPurOrdTaxAmount(convertMainData.getPurOrdTaxAmount());
        String transmissionDate = DateUtil.formatDate(new Date());
        ygOrderVO.setTransmissionDate(transmissionDate);
        ygOrderVO.setDataSouSys(convertMainData.getDataSouSys());
        for(FiConvertBaseOrderDetail fiConvertBaseOrderDetail : details) {
            YgOrderDetailVO ygOrderDetailVO = new YgOrderDetailVO();
            ygOrderDetailVO.setPurOrdNo(fiConvertBaseOrderDetail.getPurOrdNo());
            ygOrderDetailVO.setPurOrdItem(fiConvertBaseOrderDetail.getPurOrdItem());
            ygOrderDetailVO.setPrjCode(apiCompareService.getProjectCode(fiConvertBaseOrderDetail.getPrjCode()));
            ygOrderDetailVO.setItTotAmExTax(fiConvertBaseOrderDetail.getItTotAmExTax());
            BigDecimal ntaxrate = BigDecimal.ZERO;
            if(StrUtil.isNotBlank(fiConvertBaseOrderDetail.getTaxrt())) {
                ntaxrate = new BigDecimal(fiConvertBaseOrderDetail.getTaxrt());
                ntaxrate = ntaxrate.divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
            }
            ygOrderDetailVO.setTaxrt(ntaxrate.toPlainString());
            ygOrderDetailVO.setTaxAmount(fiConvertBaseOrderDetail.getTaxAmount());
            ygOrderDetailVO.setItTotAmIncTax(fiConvertBaseOrderDetail.getItTotAmIncTax());
            ygOrderDetailVO.setCeleIdInPurVou("");
            ygOrderDetailVO.setAmt(fiConvertBaseOrderDetail.getAmt());
            ygOrderDetailVO.setPrvMkCode(fiConvertBaseOrderDetail.getPrvMkCode());
            ygOrderDetailVO.setTransmissionDate(transmissionDate);
            ygOrderDetailVO.setRecepId(fiConvertBaseOrderDetail.getRecepId());
            items.add(ygOrderDetailVO);
        }
        ygOrderVO.setItems(items);
        return ygOrderVO;
    }

    /**
     * 数据转换
     *
     * @param sourceMainData
     * @param details
     */
    private void turnData(FiSourceBaseOrder sourceMainData, List<FiSourceBaseOrderDetail> details) throws Exception {
        String pk = sourceMainData.getPkMain();
        FiConvertBaseOrder fiConvertBaseOrder = new FiConvertBaseOrder();
        apiCompareCache.convertData(sourceMainData,fiConvertBaseOrder, BillTypeEnum.BASE_ORDER.getCode());
        FiConvertBaseOrder convertMainData = fiConvertBaseOrderService.getOne(Wrappers.<FiConvertBaseOrder>lambdaQuery().eq(FiConvertBaseOrder::getPkMain, pk));
        fiConvertBaseOrder.setId(null);
        if(convertMainData != null) {
            fiConvertBaseOrder.setId(convertMainData.getId());
            fiConvertBaseOrderDetailService.remove(Wrappers.<FiConvertBaseOrderDetail>lambdaQuery().eq(FiConvertBaseOrderDetail::getPkMain,pk));
        }
        // 转换预算编制保存然后走MQ推送到bip
        List<FiConvertBaseOrderDetail> convertDetailList = new ArrayList<>();
        for(FiSourceBaseOrderDetail sourceDetailData : details) {
            FiConvertBaseOrderDetail fiConvertBaseOrderDetail = new FiConvertBaseOrderDetail();
            apiCompareCache.convertData(sourceDetailData,fiConvertBaseOrderDetail, BillTypeEnum.BASE_ORDER_DETAIL.getCode());
            fiConvertBaseOrderDetail.setId(null);
            convertDetailList.add(fiConvertBaseOrderDetail);
        }
        fiConvertBaseOrderService.saveOrUpdate(fiConvertBaseOrder);
        fiConvertBaseOrderDetailService.saveOrUpdateBatch(convertDetailList);
    }

    /**
     * 订单转换前分页查询
     *
     * @return
     */
    @Override
    public Page<FiSourceBaseOrderDTO> beforeConvertDataPage(Page page, FiSourceBaseOrderQueryVO queryVO) {
        Page<FiSourceBaseOrderDTO> result = this.baseMapper.beforeConvertDataPage(page,queryVO);
        if (CollectionUtil.isNotEmpty(result.getRecords())){
            for (FiSourceBaseOrderDTO apiSource:result.getRecords()){
                ApiCompare apiCompare = apiCompareCache.getDictionaryValue(BillTypeEnum.BASE_ORDER.getCode(), "curr_cod", YgUtil.toStringTrim(apiSource.getCurrCod()));
                apiSource.setCorigcurrencyidName(Objects.isNull(apiCompare)?null:apiCompare.getTargetSystemCode());
                if(StrUtil.isNotBlank(apiSource.getSupCod())) {
                    apiSource.setPkSupplierName(dmMapper.getSupplierName(apiSource.getSupCod()));
                }
            }
        }
        return result;
    }

    /**
     * 转换前数据详情查询
     * @param page
     * @param queryVO
     * @return
     */
    @Override
    public Page<FiSourceBaseOrderDetailDTO> getBeforeConvertDataPage(Page page, FiSourceBaseOrderQueryVO queryVO) {
        Page<FiSourceBaseOrderDetailDTO> result = this.baseMapper.getBeforeConvertDataPage(page,queryVO);
        return result;
    }

    /**
     * 主体表转换后单条字段
     * @param pk
     * @return
     */
    @Override
    public List<FiConvertBaseOrderDTO> getConvertList(String pk) {
        List<FiConvertBaseOrderDTO> convertDataDTOList = new ArrayList<>();
        FiConvertBaseOrderDTO convertDataDTO = new FiConvertBaseOrderDTO();
        FiConvertBaseOrder convertDataOld = fiConvertBaseOrderService.getOne(Wrappers.<FiConvertBaseOrder>lambdaQuery().eq(FiConvertBaseOrder::getPkMain, pk));
        if(null != convertDataOld) {
            BeanUtil.copyProperties(convertDataOld,convertDataDTO);
            convertDataDTOList.add(convertDataDTO);
        }
        return convertDataDTOList;
    }

    /**
     * 获取转换后详情分页数据
     *
     * @param page
     * @param pk
     * @return
     */
    @Override
    public Page<FiConvertBaseOrderDetailDTO> getConvertData(Page page, String pk) {
        Page<FiConvertBaseOrderDetailDTO> result = this.baseMapper.getConvertData(page,pk);
        return result;
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceBaseOrder fiSourceBaseOrder = this.getById(id);
            try {
                this.pushAgain(fiSourceBaseOrder.getPkMain());
            } catch (Exception e) {
                resultList.add(fiSourceBaseOrder.getPurOrdNo()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}
